#include "MainWindow.h"
#include "GameWidget.h"
#include <QApplication>
#include <QMessageBox>
#include <QFormLayout>
#include <QDialogButtonBox>
#include <QRandomGenerator>

NewWorldDialog::NewWorldDialog(QWidget* parent)
    : QDialog(parent)
{
    setWindowTitle("Create New World");
    setModal(true);
    
    auto* layout = new QFormLayout(this);
    
    m_worldNameEdit = new QLineEdit("New World");
    layout->addRow("World Name:", m_worldNameEdit);
    
    m_seedSpinBox = new QSpinBox();
    m_seedSpinBox->setRange(0, INT_MAX);
    m_seedSpinBox->setValue(QRandomGenerator::global()->bounded(INT_MAX));
    layout->addRow("Seed:", m_seedSpinBox);
    
    auto* buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel);
    connect(buttonBox, &QDialogButtonBox::accepted, this, &QDialog::accept);
    connect(buttonBox, &QDialogButtonBox::rejected, this, &QDialog::reject);
    layout->addRow(buttonBox);
}

QString NewWorldDialog::getWorldName() const
{
    return m_worldNameEdit->text();
}

int NewWorldDialog::getSeed() const
{
    return m_seedSpinBox->value();
}

MainWindow::MainWindow(QWidget* parent)
    : QMainWindow(parent)
    , m_gameWidget(nullptr)
    , m_gameStarted(false)
{
    setWindowTitle("Minecraft Clone");
    setMinimumSize(800, 600);
    
    setupMenus();
    setupUI();
}

MainWindow::~MainWindow()
{
}

void MainWindow::setupMenus()
{
    // File menu
    auto* fileMenu = menuBar()->addMenu("&File");
    
    m_newWorldAction = fileMenu->addAction("&New World...");
    m_newWorldAction->setShortcut(QKeySequence::New);
    connect(m_newWorldAction, &QAction::triggered, this, &MainWindow::newWorld);
    
    m_loadWorldAction = fileMenu->addAction("&Load World...");
    m_loadWorldAction->setShortcut(QKeySequence::Open);
    m_loadWorldAction->setEnabled(false); // TODO: Implement world loading
    connect(m_loadWorldAction, &QAction::triggered, this, &MainWindow::loadWorld);
    
    m_saveWorldAction = fileMenu->addAction("&Save World");
    m_saveWorldAction->setShortcut(QKeySequence::Save);
    m_saveWorldAction->setEnabled(false);
    connect(m_saveWorldAction, &QAction::triggered, this, &MainWindow::saveWorld);
    
    fileMenu->addSeparator();
    
    m_exitAction = fileMenu->addAction("E&xit");
    m_exitAction->setShortcut(QKeySequence::Quit);
    connect(m_exitAction, &QAction::triggered, this, &MainWindow::exitGame);
    
    // Help menu
    auto* helpMenu = menuBar()->addMenu("&Help");
    
    m_aboutAction = helpMenu->addAction("&About");
    connect(m_aboutAction, &QAction::triggered, this, &MainWindow::showAbout);
}

void MainWindow::setupUI()
{
    m_centralWidget = new QWidget;
    setCentralWidget(m_centralWidget);
    
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    
    // Welcome screen
    auto* welcomeLabel = new QLabel("Welcome to Minecraft Clone!");
    welcomeLabel->setAlignment(Qt::AlignCenter);
    welcomeLabel->setStyleSheet("font-size: 24px; font-weight: bold; margin: 50px;");
    m_mainLayout->addWidget(welcomeLabel);
    
    auto* newWorldButton = new QPushButton("Create New World");
    newWorldButton->setMinimumHeight(40);
    connect(newWorldButton, &QPushButton::clicked, this, &MainWindow::newWorld);
    m_mainLayout->addWidget(newWorldButton);
    
    statusBar()->showMessage("Ready to create a new world");
}

void MainWindow::newWorld()
{
    NewWorldDialog dialog(this);
    if (dialog.exec() == QDialog::Accepted) {
        startGame(dialog.getWorldName(), dialog.getSeed());
    }
}

void MainWindow::loadWorld()
{
    QMessageBox::information(this, "Load World", "World loading not yet implemented.");
}

void MainWindow::saveWorld()
{
    if (m_gameWidget) {
        // TODO: Implement world saving
        statusBar()->showMessage("World saved", 2000);
    }
}

void MainWindow::exitGame()
{
    QApplication::quit();
}

void MainWindow::showAbout()
{
    QMessageBox::about(this, "About Minecraft Clone",
        "Minecraft Clone v1.0\n\n"
        "A simple voxel-based game built with Qt and OpenGL.\n"
        "Features random terrain generation and SVG textures.");
}

void MainWindow::startGame(const QString& worldName, int seed)
{
    // Clear existing layout
    QLayoutItem* item;
    while ((item = m_mainLayout->takeAt(0)) != nullptr) {
        delete item->widget();
        delete item;
    }
    
    // Create game widget
    m_gameWidget = new GameWidget(worldName, seed);
    m_mainLayout->addWidget(m_gameWidget);
    
    m_gameStarted = true;
    m_saveWorldAction->setEnabled(true);
    
    statusBar()->showMessage(QString("Playing world: %1 (Seed: %2)").arg(worldName).arg(seed));
}

#include "MainWindow.moc"
