#include "SvgTextureGenerator.h"
#include <QPainter>
#include <QRandomGenerator>
#include <QDebug>

SvgTextureGenerator::SvgTextureGenerator()
{
}

SvgTextureGenerator::~SvgTextureGenerator()
{
}

QPixmap SvgTextureGenerator::generateGrassTop(int size) const
{
    return renderSvgToPixmap(createGrassTopSvg(size), size);
}

QPixmap SvgTextureGenerator::generateGrassSide(int size) const
{
    return renderSvgToPixmap(createGrassSideSvg(size), size);
}

QPixmap SvgTextureGenerator::generateDirt(int size) const
{
    return renderSvgToPixmap(createDirtSvg(size), size);
}

QPixmap SvgTextureGenerator::generateStone(int size) const
{
    return renderSvgToPixmap(createStoneSvg(size), size);
}

QPixmap SvgTextureGenerator::generateWoodSide(int size) const
{
    return renderSvgToPixmap(createWoodSideSvg(size), size);
}

QPixmap SvgTextureGenerator::generateWoodTop(int size) const
{
    return renderSvgToPixmap(createWoodTopSvg(size), size);
}

QPixmap SvgTextureGenerator::generateLeaves(int size) const
{
    return renderSvgToPixmap(createLeavesSvg(size), size);
}

QPixmap SvgTextureGenerator::generateSand(int size) const
{
    return renderSvgToPixmap(createSandSvg(size), size);
}

QPixmap SvgTextureGenerator::generateWater(int size) const
{
    return renderSvgToPixmap(createWaterSvg(size), size);
}

QPixmap SvgTextureGenerator::generateBedrock(int size) const
{
    return renderSvgToPixmap(createBedrockSvg(size), size);
}

QPixmap SvgTextureGenerator::generateTextureAtlas(int textureSize, int atlasSize) const
{
    int totalSize = textureSize * atlasSize;
    QPixmap atlas(totalSize, totalSize);
    atlas.fill(Qt::transparent);
    
    QPainter painter(&atlas);
    
    // Define texture order (matches Block.cpp texture indices)
    QPixmap textures[16];
    textures[0] = generateGrassTop(textureSize);     // 0
    textures[1] = generateGrassSide(textureSize);    // 1
    textures[2] = generateDirt(textureSize);         // 2
    textures[3] = generateStone(textureSize);        // 3
    textures[4] = generateWoodSide(textureSize);     // 4
    textures[5] = generateWoodTop(textureSize);      // 5
    textures[6] = generateLeaves(textureSize);       // 6
    textures[7] = generateSand(textureSize);         // 7
    textures[8] = generateWater(textureSize);        // 8
    textures[9] = generateBedrock(textureSize);      // 9
    
    // Fill remaining slots with placeholder
    for (int i = 10; i < 16; ++i) {
        textures[i] = generateStone(textureSize); // Use stone as placeholder
    }
    
    // Draw textures to atlas
    for (int i = 0; i < 16; ++i) {
        int x = (i % atlasSize) * textureSize;
        int y = (i / atlasSize) * textureSize;
        painter.drawPixmap(x, y, textures[i]);
    }
    
    return atlas;
}

QString SvgTextureGenerator::createGrassTopSvg(int size) const
{
    QString noisePattern = generateNoisePattern("#4a7c59", "#5d8f6b", size, 0.4f, 123);
    
    return QString(R"(
        <svg width="%1" height="%1" xmlns="http://www.w3.org/2000/svg">
            <defs>%2</defs>
            <rect width="%1" height="%1" fill="url(#noisePattern)"/>
            <rect width="%1" height="%1" fill="#4a7c59" opacity="0.8"/>
        </svg>
    )").arg(size).arg(noisePattern);
}

QString SvgTextureGenerator::createGrassSideSvg(int size) const
{
    QString dirtNoise = generateNoisePattern("#8b4513", "#a0522d", size, 0.3f, 456);
    QString grassStrip = QString(R"(
        <rect x="0" y="0" width="%1" height="%2" fill="#4a7c59"/>
        <rect x="0" y="%2" width="%1" height="%3" fill="#8b4513"/>
    )").arg(size).arg(size / 8).arg(size - size / 8);
    
    return QString(R"(
        <svg width="%1" height="%1" xmlns="http://www.w3.org/2000/svg">
            <defs>%2</defs>
            <rect width="%1" height="%1" fill="url(#noisePattern)"/>
            %3
        </svg>
    )").arg(size).arg(dirtNoise).arg(grassStrip);
}

QString SvgTextureGenerator::createDirtSvg(int size) const
{
    QString noisePattern = generateNoisePattern("#8b4513", "#a0522d", size, 0.5f, 789);
    
    return QString(R"(
        <svg width="%1" height="%1" xmlns="http://www.w3.org/2000/svg">
            <defs>%2</defs>
            <rect width="%1" height="%1" fill="url(#noisePattern)"/>
        </svg>
    )").arg(size).arg(noisePattern);
}

QString SvgTextureGenerator::createStoneSvg(int size) const
{
    QString noisePattern = generateNoisePattern("#696969", "#808080", size, 0.6f, 101112);
    
    return QString(R"(
        <svg width="%1" height="%1" xmlns="http://www.w3.org/2000/svg">
            <defs>%2</defs>
            <rect width="%1" height="%1" fill="url(#noisePattern)"/>
        </svg>
    )").arg(size).arg(noisePattern);
}

QString SvgTextureGenerator::createWoodSideSvg(int size) const
{
    QString gradient = generateGradient("#8b4513", "#a0522d", "horizontal");
    
    QString rings;
    for (int i = 0; i < 3; ++i) {
        int y = (i + 1) * size / 4;
        rings += QString(R"(<line x1="0" y1="%1" x2="%2" y2="%1" stroke="#654321" stroke-width="1" opacity="0.5"/>)").arg(y).arg(size);
    }
    
    return QString(R"(
        <svg width="%1" height="%1" xmlns="http://www.w3.org/2000/svg">
            <defs>%2</defs>
            <rect width="%1" height="%1" fill="url(#gradient)"/>
            %3
        </svg>
    )").arg(size).arg(gradient).arg(rings);
}

QString SvgTextureGenerator::createWoodTopSvg(int size) const
{
    QString rings;
    int center = size / 2;
    for (int i = 1; i <= 4; ++i) {
        int radius = i * size / 8;
        rings += QString(R"(<circle cx="%1" cy="%1" r="%2" fill="none" stroke="#654321" stroke-width="1" opacity="0.6"/>)").arg(center).arg(radius);
    }
    
    return QString(R"(
        <svg width="%1" height="%1" xmlns="http://www.w3.org/2000/svg">
            <rect width="%1" height="%1" fill="#8b4513"/>
            %2
        </svg>
    )").arg(size).arg(rings);
}

QString SvgTextureGenerator::createLeavesSvg(int size) const
{
    QString noisePattern = generateNoisePattern("#228b22", "#32cd32", size, 0.7f, 131415);
    
    return QString(R"(
        <svg width="%1" height="%1" xmlns="http://www.w3.org/2000/svg">
            <defs>%2</defs>
            <rect width="%1" height="%1" fill="url(#noisePattern)"/>
        </svg>
    )").arg(size).arg(noisePattern);
}

QString SvgTextureGenerator::createSandSvg(int size) const
{
    QString noisePattern = generateNoisePattern("#f4a460", "#daa520", size, 0.4f, 161718);
    
    return QString(R"(
        <svg width="%1" height="%1" xmlns="http://www.w3.org/2000/svg">
            <defs>%2</defs>
            <rect width="%1" height="%1" fill="url(#noisePattern)"/>
        </svg>
    )").arg(size).arg(noisePattern);
}

QString SvgTextureGenerator::createWaterSvg(int size) const
{
    QString gradient = generateGradient("#4169e1", "#1e90ff", "vertical");
    
    return QString(R"(
        <svg width="%1" height="%1" xmlns="http://www.w3.org/2000/svg">
            <defs>%2</defs>
            <rect width="%1" height="%1" fill="url(#gradient)" opacity="0.8"/>
        </svg>
    )").arg(size).arg(gradient);
}

QString SvgTextureGenerator::createBedrockSvg(int size) const
{
    QString noisePattern = generateNoisePattern("#2f2f2f", "#1c1c1c", size, 0.8f, 192021);
    
    return QString(R"(
        <svg width="%1" height="%1" xmlns="http://www.w3.org/2000/svg">
            <defs>%2</defs>
            <rect width="%1" height="%1" fill="url(#noisePattern)"/>
        </svg>
    )").arg(size).arg(noisePattern);
}

QPixmap SvgTextureGenerator::renderSvgToPixmap(const QString& svgContent, int size) const
{
    QSvgRenderer renderer(svgContent.toUtf8());
    QPixmap pixmap(size, size);
    pixmap.fill(Qt::transparent);
    
    QPainter painter(&pixmap);
    renderer.render(&painter);
    
    return pixmap;
}

QString SvgTextureGenerator::generateNoisePattern(const QString& baseColor, const QString& noiseColor, 
                                                 int size, float density, int seed) const
{
    QRandomGenerator rng(seed);
    QString circles;
    
    int numCircles = static_cast<int>(size * size * density / 100.0f);
    
    for (int i = 0; i < numCircles; ++i) {
        int x = rng.bounded(size);
        int y = rng.bounded(size);
        int radius = rng.bounded(1, 4);
        float opacity = 0.3f + (rng.generateDouble() * 0.4f);
        
        circles += QString(R"(<circle cx="%1" cy="%2" r="%3" fill="%4" opacity="%5"/>)")
                   .arg(x).arg(y).arg(radius).arg(noiseColor).arg(opacity);
    }
    
    return QString(R"(
        <pattern id="noisePattern" patternUnits="userSpaceOnUse" width="%1" height="%1">
            <rect width="%1" height="%1" fill="%2"/>
            %3
        </pattern>
    )").arg(size).arg(baseColor).arg(circles);
}

QString SvgTextureGenerator::generateGradient(const QString& color1, const QString& color2, 
                                             const QString& direction) const
{
    QString coords = (direction == "horizontal") ? 
        R"(x1="0%" y1="0%" x2="100%" y2="0%")" : 
        R"(x1="0%" y1="0%" x2="0%" y2="100%")";
    
    return QString(R"(
        <linearGradient id="gradient" %1>
            <stop offset="0%" stop-color="%2"/>
            <stop offset="100%" stop-color="%3"/>
        </linearGradient>
    )").arg(coords).arg(color1).arg(color2);
}
