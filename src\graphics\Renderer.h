#pragma once

#include <QOpenGLFunctions>
#include <QOpenGLShaderProgram>
#include <memory>

class World;
class Camera;
class TextureManager;

class Renderer : protected QOpenGLFunctions
{
public:
    Renderer();
    ~Renderer();
    
    // Initialization
    bool initialize();
    void cleanup();
    
    // Rendering
    void render(const World& world, const Camera& camera, int screenWidth, int screenHeight);
    
    // Settings
    void setWireframeMode(bool enabled) { m_wireframeMode = enabled; }
    bool isWireframeMode() const { return m_wireframeMode; }
    
    void setRenderDistance(float distance) { m_renderDistance = distance; }
    float getRenderDistance() const { return m_renderDistance; }
    
private:
    bool createShaders();
    void setupUniforms(const Camera& camera);
    
    std::unique_ptr<QOpenGLShaderProgram> m_shaderProgram;
    std::unique_ptr<TextureManager> m_textureManager;
    
    // Shader uniform locations
    int m_mvpMatrixLocation;
    int m_textureLocation;
    
    // Rendering settings
    bool m_wireframeMode;
    float m_renderDistance;
    bool m_initialized;
    
    // Vertex shader source
    static const char* vertexShaderSource;
    
    // Fragment shader source
    static const char* fragmentShaderSource;
};
