#include "SimpleMainWindow.h"
#include "GameWidget.h"
#include <QRandomGenerator>

SimpleMainWindow::SimpleMainWindow(QWidget* parent)
    : QMainWindow(parent)
    , m_gameWidget(nullptr)
    , m_gameStarted(false)
{
    setWindowTitle("Minecraft Clone");
    setMinimumSize(800, 600);
    setupUI();
}

SimpleMainWindow::~SimpleMainWindow()
{
}

void SimpleMainWindow::setupUI()
{
    m_centralWidget = new QWidget;
    setCentralWidget(m_centralWidget);
    
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    
    // Welcome screen
    auto* welcomeLabel = new QLabel("Welcome to Minecraft Clone!");
    welcomeLabel->setAlignment(Qt::AlignCenter);
    welcomeLabel->setStyleSheet("font-size: 24px; font-weight: bold; margin: 50px;");
    m_mainLayout->addWidget(welcomeLabel);
    
    auto* startButton = new QPushButton("Start Game");
    startButton->setMinimumHeight(40);
    connect(startButton, &QPushButton::clicked, this, &SimpleMainWindow::startGame);
    m_mainLayout->addWidget(startButton);
    
    statusBar()->showMessage("Click 'Start Game' to begin");
}

void SimpleMainWindow::startGame()
{
    if (m_gameStarted) {
        return;
    }
    
    // Clear existing layout
    QLayoutItem* item;
    while ((item = m_mainLayout->takeAt(0)) != nullptr) {
        delete item->widget();
        delete item;
    }
    
    // Create game widget with default world
    QString worldName = "Test World";
    int seed = QRandomGenerator::global()->bounded(INT_MAX);
    
    m_gameWidget = new GameWidget(worldName, seed);
    m_mainLayout->addWidget(m_gameWidget);
    
    m_gameStarted = true;
    statusBar()->showMessage(QString("Playing world: %1 (Seed: %2)").arg(worldName).arg(seed));
}

#include "SimpleMainWindow.moc"
