#include <QApplication>
#include <QOpenGLWidget>
#include <QOpenGLContext>
#include <QDebug>
#include "src/graphics/Mesh.h"

class TestWidget : public QOpenGLWidget
{
public:
    TestWidget() {}
    
protected:
    void initializeGL() override
    {
        qDebug() << "Testing Mesh class...";
        
        // Test mesh creation
        Mesh mesh;
        
        // Test with some dummy data
        std::vector<float> vertices = {
            0.0f, 0.0f, 0.0f, 0.0f, 0.0f,
            1.0f, 0.0f, 0.0f, 1.0f, 0.0f,
            0.5f, 1.0f, 0.0f, 0.5f, 1.0f
        };
        
        std::vector<unsigned int> indices = {0, 1, 2};
        
        mesh.updateData(vertices, indices);
        
        qDebug() << "Mesh vertex count:" << mesh.getVertexCount();
        qDebug() << "Mesh index count:" << mesh.getIndexCount();
        qDebug() << "Mesh is empty:" << mesh.isEmpty();
        
        // Test rendering (should not crash)
        mesh.render();
        
        qDebug() << "Mesh test completed successfully!";
    }
    
    void paintGL() override
    {
        glClear(GL_COLOR_BUFFER_BIT);
    }
    
    void resizeGL(int w, int h) override
    {
        glViewport(0, 0, w, h);
    }
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    TestWidget widget;
    widget.show();
    
    qDebug() << "Mesh test widget created. Close window to exit.";
    
    return app.exec();
}
