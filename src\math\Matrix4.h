#pragma once

#include "Vector3.h"

class Matrix4
{
public:
    float m[16]; // Column-major order
    
    Matrix4();
    Matrix4(const float* data);
    
    // Access operators
    float& operator[](int index) { return m[index]; }
    const float& operator[](int index) const { return m[index]; }
    
    // Matrix operations
    Matrix4 operator*(const Matrix4& other) const;
    Vector3 operator*(const Vector3& vec) const;
    Matrix4& operator*=(const Matrix4& other);
    
    // Transformation matrices
    static Matrix4 identity();
    static Matrix4 translation(const Vector3& translation);
    static Matrix4 rotation(const Vector3& axis, float angle);
    static Matrix4 scale(const Vector3& scale);
    static Matrix4 perspective(float fov, float aspect, float near, float far);
    static Matrix4 lookAt(const Vector3& eye, const Vector3& center, const Vector3& up);
    
    // Utility functions
    Matrix4 transposed() const;
    Matrix4 inverted() const;
    void transpose();
    void invert();
    
    // Get data pointer for OpenGL
    const float* data() const { return m; }
    float* data() { return m; }
};
