#include "Mesh.h"
#include <QOpenGLContext>
#include <QOpenGLExtraFunctions>

Mesh::Mesh()
    : m_VAO(0)
    , m_VBO(0)
    , m_EBO(0)
    , m_vertexCount(0)
    , m_indexCount(0)
    , m_initialized(false)
{
    if (QOpenGLContext::currentContext()) {
        initializeOpenGLFunctions();
        setupBuffers();
    }
}

Mesh::~Mesh()
{
    cleanup();
}

void Mesh::setupBuffers()
{
    if (m_initialized) {
        return;
    }
    
    // Generate buffers
    glGenVertexArrays(1, &m_VAO);
    glGenBuffers(1, &m_VBO);
    glGenBuffers(1, &m_EBO);
    
    m_initialized = true;
}

void Mesh::updateData(const std::vector<float>& vertices, const std::vector<unsigned int>& indices)
{
    if (!m_initialized) {
        initializeOpenGLFunctions();
        setupBuffers();
    }
    
    m_vertexCount = vertices.size() / 5; // 5 floats per vertex (3 pos + 2 tex)
    m_indexCount = indices.size();
    
    if (m_indexCount == 0) {
        return;
    }
    
    // Bind VAO
    glBindVertexArray(m_VAO);
    
    // Upload vertex data
    glBindBuffer(GL_ARRAY_BUFFER, m_VBO);
    glBufferData(GL_ARRAY_BUFFER, vertices.size() * sizeof(float), vertices.data(), GL_STATIC_DRAW);
    
    // Upload index data
    glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, m_EBO);
    glBufferData(GL_ELEMENT_ARRAY_BUFFER, indices.size() * sizeof(unsigned int), indices.data(), GL_STATIC_DRAW);
    
    // Set vertex attributes
    // Position attribute (location 0)
    glVertexAttribPointer(0, 3, GL_FLOAT, GL_FALSE, 5 * sizeof(float), (void*)0);
    glEnableVertexAttribArray(0);
    
    // Texture coordinate attribute (location 1)
    glVertexAttribPointer(1, 2, GL_FLOAT, GL_FALSE, 5 * sizeof(float), (void*)(3 * sizeof(float)));
    glEnableVertexAttribArray(1);
    
    // Unbind VAO
    glBindVertexArray(0);
}

void Mesh::render() const
{
    if (!m_initialized || m_indexCount == 0) {
        return;
    }

    // Get current OpenGL context functions
    QOpenGLExtraFunctions* gl = QOpenGLContext::currentContext()->extraFunctions();
    if (!gl) {
        return;
    }

    gl->glBindVertexArray(m_VAO);
    gl->glDrawElements(GL_TRIANGLES, static_cast<GLsizei>(m_indexCount), GL_UNSIGNED_INT, 0);
    gl->glBindVertexArray(0);
}

void Mesh::clear()
{
    m_vertexCount = 0;
    m_indexCount = 0;
}

void Mesh::cleanup()
{
    if (m_initialized && QOpenGLContext::currentContext()) {
        glDeleteVertexArrays(1, &m_VAO);
        glDeleteBuffers(1, &m_VBO);
        glDeleteBuffers(1, &m_EBO);
    }
    
    m_VAO = m_VBO = m_EBO = 0;
    m_initialized = false;
}
