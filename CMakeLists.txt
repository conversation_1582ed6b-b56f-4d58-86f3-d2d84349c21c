cmake_minimum_required(VERSION 3.16)
project(MinecraftClone VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt6 components
find_package(Qt6 REQUIRED COMPONENTS Core Widgets OpenGL OpenGLWidgets Svg)

# Enable automatic MOC, UIC, and RCC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Find OpenGL
find_package(OpenGL REQUIRED)

# Include directories
include_directories(src)
include_directories(src/core)
include_directories(src/graphics)
include_directories(src/world)
include_directories(src/ui)
include_directories(src/textures)
include_directories(src/math)

# Source files
set(SOURCES
    src/main.cpp
    src/core/Game.cpp
    src/core/Block.cpp
    src/core/Chunk.cpp
    src/core/World.cpp
    src/graphics/Renderer.cpp
    src/graphics/Camera.cpp
    src/graphics/Mesh.cpp
    src/world/TerrainGenerator.cpp
    src/world/NoiseGenerator.cpp
    src/ui/MainWindow.cpp
    src/ui/GameWidget.cpp
    src/textures/TextureManager.cpp
    src/textures/SvgTextureGenerator.cpp
    src/math/Vector3.cpp
    src/math/Matrix4.cpp
)

# Header files
set(HEADERS
    src/core/Game.h
    src/core/Block.h
    src/core/Chunk.h
    src/core/World.h
    src/graphics/Renderer.h
    src/graphics/Camera.h
    src/graphics/Mesh.h
    src/world/TerrainGenerator.h
    src/world/NoiseGenerator.h
    src/ui/MainWindow.h
    src/ui/GameWidget.h
    src/textures/TextureManager.h
    src/textures/SvgTextureGenerator.h
    src/math/Vector3.h
    src/math/Matrix4.h
)

# Create executable
add_executable(MinecraftClone ${SOURCES} ${HEADERS})

# Link libraries
target_link_libraries(MinecraftClone
    Qt6::Core
    Qt6::Widgets
    Qt6::OpenGL
    Qt6::OpenGLWidgets
    Qt6::Svg
    OpenGL::GL
)

# Set output directory
set_target_properties(MinecraftClone PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Copy resources to build directory
file(COPY resources DESTINATION ${CMAKE_BINARY_DIR})
