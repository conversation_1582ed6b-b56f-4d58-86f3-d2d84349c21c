#include <QCoreApplication>
#include <QDebug>
#include "src/math/Vector3.h"
#include "src/math/Matrix4.h"
#include "src/core/Block.h"
#include "src/world/NoiseGenerator.h"

void testVector3()
{
    qDebug() << "=== Testing Vector3 ===";
    
    Vector3 v1(1, 2, 3);
    Vector3 v2(4, 5, 6);
    Vector3 v3 = v1 + v2;
    
    qDebug() << "v1 + v2 =" << v3.x << v3.y << v3.z;
    qDebug() << "Length of v3:" << v3.length();
    
    Vector3 normalized = v3.normalized();
    qDebug() << "Normalized:" << normalized.x << normalized.y << normalized.z;
    qDebug() << "Normalized length:" << normalized.length();
}

void testMatrix4()
{
    qDebug() << "=== Testing Matrix4 ===";
    
    Matrix4 identity = Matrix4::identity();
    qDebug() << "Identity matrix [0]:" << identity[0];
    qDebug() << "Identity matrix [5]:" << identity[5];
    
    Matrix4 translation = Matrix4::translation(Vector3(10, 20, 30));
    qDebug() << "Translation matrix [12]:" << translation[12];
    qDebug() << "Translation matrix [13]:" << translation[13];
    qDebug() << "Translation matrix [14]:" << translation[14];
    
    Vector3 point(1, 1, 1);
    Vector3 transformed = translation * point;
    qDebug() << "Transformed point:" << transformed.x << transformed.y << transformed.z;
}

void testBlock()
{
    qDebug() << "=== Testing Block ===";
    
    Block grassBlock(BlockType::Grass);
    qDebug() << "Grass block type:" << Block::getBlockName(grassBlock.getType());
    qDebug() << "Is solid:" << grassBlock.isSolid();
    qDebug() << "Is transparent:" << grassBlock.isTransparent();
    
    Block airBlock(BlockType::Air);
    qDebug() << "Air block type:" << Block::getBlockName(airBlock.getType());
    qDebug() << "Is solid:" << airBlock.isSolid();
    
    // Test face rendering logic
    bool shouldRender = Block::shouldRenderFace(BlockType::Grass, BlockType::Air);
    qDebug() << "Should render grass face against air:" << shouldRender;
    
    shouldRender = Block::shouldRenderFace(BlockType::Grass, BlockType::Grass);
    qDebug() << "Should render grass face against grass:" << shouldRender;
}

void testNoiseGenerator()
{
    qDebug() << "=== Testing NoiseGenerator ===";
    
    NoiseGenerator noise(12345);
    
    float noise2D = noise.noise2D(0.5f, 0.5f);
    qDebug() << "2D Noise at (0.5, 0.5):" << noise2D;
    
    float octaveNoise = noise.octaveNoise2D(0.5f, 0.5f, 4, 0.5f, 0.01f);
    qDebug() << "Octave noise:" << octaveNoise;
    
    // Test noise consistency
    float noise1 = noise.noise2D(1.0f, 1.0f);
    float noise2 = noise.noise2D(1.0f, 1.0f);
    qDebug() << "Noise consistency test:" << (noise1 == noise2 ? "PASS" : "FAIL");
    
    // Test different positions
    for (int i = 0; i < 5; ++i) {
        float x = i * 0.1f;
        float y = i * 0.1f;
        float n = noise.noise2D(x, y);
        qDebug() << "Noise at (" << x << "," << y << "):" << n;
    }
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "Starting Minecraft Clone Core Tests...";
    qDebug() << "";
    
    testVector3();
    qDebug() << "";
    
    testMatrix4();
    qDebug() << "";
    
    testBlock();
    qDebug() << "";
    
    testNoiseGenerator();
    qDebug() << "";
    
    qDebug() << "All core tests completed successfully!";
    qDebug() << "The basic math and game logic systems are working.";
    qDebug() << "";
    qDebug() << "Next: Open MinecraftClone.pro in Qt Creator to run the full game!";
    
    return 0;
}
