#pragma once

#include <QOpenGLFunctions>
#include <QPixmap>
#include <memory>

class SvgTextureGenerator;

class TextureManager : protected QOpenGLFunctions
{
public:
    TextureManager();
    ~TextureManager();
    
    // Initialize OpenGL textures
    bool initialize();
    void cleanup();
    
    // Texture binding
    void bindTextureAtlas();
    unsigned int getTextureAtlasId() const { return m_textureAtlasId; }
    
    // Texture generation
    void generateTextures();
    void reloadTextures();
    
    // Settings
    void setTextureSize(int size) { m_textureSize = size; }
    int getTextureSize() const { return m_textureSize; }
    
    void setAtlasSize(int size) { m_atlasSize = size; }
    int getAtlasSize() const { return m_atlasSize; }
    
private:
    void createTextureAtlas();
    unsigned int createOpenGLTexture(const QPixmap& pixmap);
    
    std::unique_ptr<SvgTextureGenerator> m_svgGenerator;
    
    unsigned int m_textureAtlasId;
    int m_textureSize;
    int m_atlasSize;
    bool m_initialized;
};
