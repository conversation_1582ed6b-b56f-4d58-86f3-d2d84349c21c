#pragma once

#include <QString>
#include <QPixmap>
#include <QSvgRenderer>
#include <memory>

class SvgTextureGenerator
{
public:
    SvgTextureGenerator();
    ~SvgTextureGenerator();
    
    // Generate individual block textures
    QPixmap generateGrassTop(int size = 64) const;
    QPixmap generateGrassSide(int size = 64) const;
    QPixmap generateDirt(int size = 64) const;
    QPixmap generateStone(int size = 64) const;
    QPixmap generateWoodSide(int size = 64) const;
    QPixmap generateWoodTop(int size = 64) const;
    QPixmap generateLeaves(int size = 64) const;
    QPixmap generateSand(int size = 64) const;
    QPixmap generateWater(int size = 64) const;
    QPixmap generateBedrock(int size = 64) const;
    
    // Generate texture atlas
    QPixmap generateTextureAtlas(int textureSize = 64, int atlasSize = 4) const;
    
private:
    // SVG generation helpers
    QString createGrassTopSvg(int size) const;
    QString createGrassSideSvg(int size) const;
    QString createDirtSvg(int size) const;
    QString createStoneSvg(int size) const;
    QString createWoodSideSvg(int size) const;
    QString createWoodTopSvg(int size) const;
    QString createLeavesSvg(int size) const;
    QString createSandSvg(int size) const;
    QString createWaterSvg(int size) const;
    QString createBedrockSvg(int size) const;
    
    // Utility functions
    QPixmap renderSvgToPixmap(const QString& svgContent, int size) const;
    QString generateNoisePattern(const QString& baseColor, const QString& noiseColor, 
                                int size, float density = 0.3f, int seed = 42) const;
    QString generateGradient(const QString& color1, const QString& color2, 
                           const QString& direction = "vertical") const;
};
