#pragma once

#include <QOpenGLWidget>
#include <QOpenGLFunctions>
#include <QTimer>
#include <QKeyEvent>
#include <QMouseEvent>
#include <QElapsedTimer>
#include <memory>

class World;
class Camera;
class Renderer;

class GameWidget : public QOpenGLWidget, protected QOpenGLFunctions
{
    Q_OBJECT
    
public:
    explicit GameWidget(const QString& worldName, int seed, QWidget* parent = nullptr);
    ~GameWidget();
    
protected:
    // OpenGL functions
    void initializeGL() override;
    void paintGL() override;
    void resizeGL(int width, int height) override;
    
    // Input handling
    void keyPressEvent(QKeyEvent* event) override;
    void keyReleaseEvent(QKeyEvent* event) override;
    void mousePressEvent(QMouseEvent* event) override;
    void mouseMoveEvent(QMouseEvent* event) override;
    void wheelEvent(QWheelEvent* event) override;
    
private slots:
    void update();
    
private:
    void setupInputHandling();
    void updateCamera(float deltaTime);
    void handleInput(float deltaTime);
    
    std::unique_ptr<World> m_world;
    std::unique_ptr<Camera> m_camera;
    std::unique_ptr<Renderer> m_renderer;
    
    QTimer* m_updateTimer;
    QString m_worldName;
    int m_seed;
    
    // Input state
    bool m_keys[256];
    QPoint m_lastMousePos;
    bool m_mouseCaptured;
    
    // Timing
    QElapsedTimer m_frameTimer;
    qint64 m_lastFrameTime;
    float m_deltaTime;
    
    // Camera movement
    float m_cameraSpeed;
    float m_mouseSensitivity;
};
