#include "TerrainGenerator.h"
#include "core/Chunk.h"
#include <cmath>
#include <random>

TerrainGenerator::TerrainGenerator(int seed)
    : m_seed(seed)
{
    // Create noise generators with different seeds
    m_heightNoise = std::make_unique<NoiseGenerator>(seed);
    m_caveNoise = std::make_unique<NoiseGenerator>(seed + 1);
    m_oreNoise = std::make_unique<NoiseGenerator>(seed + 2);
    m_temperatureNoise = std::make_unique<NoiseGenerator>(seed + 3);
    m_humidityNoise = std::make_unique<NoiseGenerator>(seed + 4);
}

TerrainGenerator::~TerrainGenerator()
{
}

void TerrainGenerator::generateChunk(Chunk& chunk)
{
    generateTerrain(chunk);
    generateCaves(chunk);
    generateOres(chunk);
    generateTrees(chunk);
    
    chunk.setGenerated(true);
    chunk.rebuildMesh();
}

void TerrainGenerator::generateTerrain(Chunk& chunk)
{
    Vector3 chunkWorldPos = chunk.getWorldPosition();
    
    for (int x = 0; x < Chunk::CHUNK_SIZE; ++x) {
        for (int z = 0; z < Chunk::CHUNK_SIZE; ++z) {
            int worldX = chunkWorldPos.x + x;
            int worldZ = chunkWorldPos.z + z;
            
            int surfaceHeight = getHeightAt(worldX, worldZ);
            float temperature = getTemperatureAt(worldX, worldZ);
            float humidity = getHumidityAt(worldX, worldZ);
            BiomeType biome = getBiome(temperature, humidity);
            
            // Generate column
            for (int y = 0; y < Chunk::CHUNK_HEIGHT; ++y) {
                BlockType blockType = BlockType::Air;
                
                if (y == 0) {
                    // Bedrock layer
                    blockType = BlockType::Bedrock;
                } else if (y <= surfaceHeight) {
                    blockType = getBlockForBiome(biome, y, surfaceHeight, y < surfaceHeight - 3);
                } else if (y <= m_settings.seaLevel && biome == BiomeType::Ocean) {
                    blockType = BlockType::Water;
                }
                
                chunk.setBlock(x, y, z, Block(blockType));
            }
        }
    }
}

void TerrainGenerator::generateCaves(Chunk& chunk)
{
    Vector3 chunkWorldPos = chunk.getWorldPosition();
    
    for (int x = 0; x < Chunk::CHUNK_SIZE; ++x) {
        for (int y = 1; y < m_settings.seaLevel; ++y) { // Only generate caves below sea level
            for (int z = 0; z < Chunk::CHUNK_SIZE; ++z) {
                int worldX = chunkWorldPos.x + x;
                int worldZ = chunkWorldPos.z + z;
                
                float caveNoise = m_caveNoise->octaveNoise3D(
                    worldX * m_settings.caveScale,
                    y * m_settings.caveScale,
                    worldZ * m_settings.caveScale,
                    m_settings.caveOctaves
                );
                
                if (caveNoise > m_settings.caveDensity) {
                    Block currentBlock = chunk.getBlock(x, y, z);
                    if (currentBlock.getType() != BlockType::Bedrock) {
                        chunk.setBlock(x, y, z, Block(BlockType::Air));
                    }
                }
            }
        }
    }
}

void TerrainGenerator::generateOres(Chunk& chunk)
{
    Vector3 chunkWorldPos = chunk.getWorldPosition();
    std::mt19937 rng(m_seed + chunkWorldPos.x * 1000 + chunkWorldPos.z);
    
    for (int x = 0; x < Chunk::CHUNK_SIZE; ++x) {
        for (int y = 1; y < 64; ++y) { // Ores only in lower levels
            for (int z = 0; z < Chunk::CHUNK_SIZE; ++z) {
                Block currentBlock = chunk.getBlock(x, y, z);
                if (currentBlock.getType() != BlockType::Stone) {
                    continue;
                }
                
                // Simple ore generation based on height and random chance
                float oreChance = rng() / static_cast<float>(rng.max());
                
                if (y < 16 && oreChance < 0.001f) {
                    // Very rare deep ores - use stone for now
                    // Could add diamond, gold, etc.
                } else if (y < 32 && oreChance < 0.005f) {
                    // Uncommon ores
                } else if (y < 48 && oreChance < 0.01f) {
                    // Common ores
                }
            }
        }
    }
}

void TerrainGenerator::generateTrees(Chunk& chunk)
{
    Vector3 chunkWorldPos = chunk.getWorldPosition();
    std::mt19937 rng(m_seed + chunkWorldPos.x * 1337 + chunkWorldPos.z * 7331);
    
    for (int x = 2; x < Chunk::CHUNK_SIZE - 2; ++x) {
        for (int z = 2; z < Chunk::CHUNK_SIZE - 2; ++z) {
            int worldX = chunkWorldPos.x + x;
            int worldZ = chunkWorldPos.z + z;
            
            float temperature = getTemperatureAt(worldX, worldZ);
            float humidity = getHumidityAt(worldX, worldZ);
            BiomeType biome = getBiome(temperature, humidity);
            
            if (biome == BiomeType::Forest || biome == BiomeType::Plains) {
                float treeChance = rng() / static_cast<float>(rng.max());
                float treeDensity = (biome == BiomeType::Forest) ? 0.1f : 0.02f;
                
                if (treeChance < treeDensity) {
                    int surfaceHeight = getHeightAt(worldX, worldZ);
                    
                    // Check if surface is grass
                    Block surfaceBlock = chunk.getBlock(x, surfaceHeight, z);
                    if (surfaceBlock.getType() == BlockType::Grass) {
                        // Generate simple tree
                        int treeHeight = 4 + (rng() % 3); // 4-6 blocks tall
                        
                        // Tree trunk
                        for (int y = surfaceHeight + 1; y <= surfaceHeight + treeHeight; ++y) {
                            if (y < Chunk::CHUNK_HEIGHT) {
                                chunk.setBlock(x, y, z, Block(BlockType::Wood));
                            }
                        }
                        
                        // Tree leaves
                        int leafStart = surfaceHeight + treeHeight - 2;
                        for (int ly = leafStart; ly <= surfaceHeight + treeHeight + 1; ++ly) {
                            if (ly >= Chunk::CHUNK_HEIGHT) break;
                            
                            int radius = (ly == leafStart || ly == surfaceHeight + treeHeight + 1) ? 1 : 2;
                            
                            for (int lx = x - radius; lx <= x + radius; ++lx) {
                                for (int lz = z - radius; lz <= z + radius; ++lz) {
                                    if (lx >= 0 && lx < Chunk::CHUNK_SIZE && 
                                        lz >= 0 && lz < Chunk::CHUNK_SIZE) {
                                        
                                        Block currentBlock = chunk.getBlock(lx, ly, lz);
                                        if (currentBlock.getType() == BlockType::Air) {
                                            chunk.setBlock(lx, ly, lz, Block(BlockType::Leaves));
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

int TerrainGenerator::getHeightAt(int worldX, int worldZ) const
{
    float heightNoise = m_heightNoise->octaveNoise2D(
        worldX * m_settings.heightScale,
        worldZ * m_settings.heightScale,
        m_settings.heightOctaves,
        0.5f,
        1.0f
    );
    
    // Normalize to 0-1 range
    heightNoise = (heightNoise + 1.0f) * 0.5f;
    
    // Scale to height range
    int height = m_settings.minHeight + 
                static_cast<int>(heightNoise * (m_settings.maxHeight - m_settings.minHeight));
    
    return std::max(m_settings.minHeight, std::min(m_settings.maxHeight, height));
}

float TerrainGenerator::getTemperatureAt(int worldX, int worldZ) const
{
    return m_temperatureNoise->octaveNoise2D(
        worldX * m_settings.temperatureScale,
        worldZ * m_settings.temperatureScale,
        4
    );
}

float TerrainGenerator::getHumidityAt(int worldX, int worldZ) const
{
    return m_humidityNoise->octaveNoise2D(
        worldX * m_settings.humidityScale,
        worldZ * m_settings.humidityScale,
        4
    );
}

TerrainGenerator::BiomeType TerrainGenerator::getBiome(float temperature, float humidity) const
{
    if (temperature < -0.3f) {
        return BiomeType::Mountains;
    } else if (temperature > 0.3f && humidity < -0.2f) {
        return BiomeType::Desert;
    } else if (humidity > 0.2f) {
        return BiomeType::Forest;
    } else if (temperature < -0.1f) {
        return BiomeType::Ocean;
    } else {
        return BiomeType::Plains;
    }
}

BlockType TerrainGenerator::getBlockForBiome(BiomeType biome, int height, int surfaceHeight, bool isUnderground) const
{
    if (isUnderground) {
        return BlockType::Stone;
    }
    
    if (height == surfaceHeight) {
        // Surface block
        switch (biome) {
            case BiomeType::Desert:
                return BlockType::Sand;
            case BiomeType::Ocean:
                return (height <= m_settings.seaLevel) ? BlockType::Sand : BlockType::Grass;
            default:
                return BlockType::Grass;
        }
    } else if (height >= surfaceHeight - 3) {
        // Subsurface blocks
        switch (biome) {
            case BiomeType::Desert:
                return BlockType::Sand;
            default:
                return BlockType::Dirt;
        }
    } else {
        return BlockType::Stone;
    }
}
