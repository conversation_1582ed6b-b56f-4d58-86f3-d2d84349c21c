#pragma once

#include <QOpenGLFunctions>
#include <QOpenGLExtraFunctions>
#include <vector>

class Mesh : protected QOpenGLExtraFunctions
{
public:
    Mesh();
    ~Mesh();
    
    // Data management
    void updateData(const std::vector<float>& vertices, const std::vector<unsigned int>& indices);
    void clear();
    
    // Rendering
    void render() const;
    
    // Info
    bool isEmpty() const { return m_indexCount == 0; }
    unsigned int getVertexCount() const { return m_vertexCount; }
    unsigned int getIndexCount() const { return m_indexCount; }
    
private:
    void setupBuffers();
    void cleanup();
    
    unsigned int m_VAO; // Vertex Array Object
    unsigned int m_VBO; // Vertex Buffer Object
    unsigned int m_EBO; // Element Buffer Object
    
    unsigned int m_vertexCount;
    unsigned int m_indexCount;
    
    bool m_initialized;
};
