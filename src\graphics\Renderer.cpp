#include "Renderer.h"
#include "Camera.h"
#include "Mesh.h"
#include "core/World.h"
#include "core/Chunk.h"
#include "textures/TextureManager.h"
#include <QDebug>

const char* Renderer::vertexShaderSource = R"(
#version 330 core

layout (location = 0) in vec3 aPos;
layout (location = 1) in vec2 aTexCoord;

uniform mat4 uMVPMatrix;

out vec2 TexCoord;

void main()
{
    gl_Position = uMVPMatrix * vec4(aPos, 1.0);
    TexCoord = aTexCoord;
}
)";

const char* Renderer::fragmentShaderSource = R"(
#version 330 core

in vec2 TexCoord;
out vec4 FragColor;

uniform sampler2D uTexture;

void main()
{
    FragColor = texture(uTexture, TexCoord);
    
    // Simple fog effect based on depth
    float depth = gl_FragCoord.z;
    float fogFactor = clamp((depth - 0.85) * 10.0, 0.0, 1.0);
    vec3 fogColor = vec3(0.5, 0.8, 1.0); // Sky blue
    
    FragColor.rgb = mix(FragColor.rgb, fogColor, fogFactor);
}
)";

Renderer::Renderer()
    : m_textureManager(std::make_unique<TextureManager>())
    , m_mvpMatrixLocation(-1)
    , m_textureLocation(-1)
    , m_wireframeMode(false)
    , m_renderDistance(128.0f)
    , m_initialized(false)
{
}

Renderer::~Renderer()
{
    cleanup();
}

bool Renderer::initialize()
{
    if (m_initialized) {
        return true;
    }
    
    initializeOpenGLFunctions();
    
    // Initialize texture manager
    if (!m_textureManager->initialize()) {
        qWarning() << "Failed to initialize texture manager";
        return false;
    }
    
    // Create shaders
    if (!createShaders()) {
        qWarning() << "Failed to create shaders";
        return false;
    }
    
    m_initialized = true;
    qDebug() << "Renderer initialized successfully";
    return true;
}

void Renderer::cleanup()
{
    if (m_initialized) {
        m_shaderProgram.reset();
        m_textureManager.reset();
    }
    
    m_initialized = false;
}

void Renderer::render(const World& world, const Camera& camera, int screenWidth, int screenHeight)
{
    if (!m_initialized) {
        return;
    }
    
    // Set viewport
    glViewport(0, 0, screenWidth, screenHeight);
    
    // Use shader program
    m_shaderProgram->bind();
    
    // Setup uniforms
    setupUniforms(camera);
    
    // Bind texture atlas
    m_textureManager->bindTextureAtlas();
    
    // Set wireframe mode
    if (m_wireframeMode) {
        glPolygonMode(GL_FRONT_AND_BACK, GL_LINE);
    } else {
        glPolygonMode(GL_FRONT_AND_BACK, GL_FILL);
    }
    
    // Get visible chunks
    std::vector<Chunk*> visibleChunks = world.getVisibleChunks(camera.getPosition(), m_renderDistance);
    
    // Render each chunk
    for (Chunk* chunk : visibleChunks) {
        if (chunk && chunk->getMesh() && !chunk->getMesh()->isEmpty()) {
            // Calculate model matrix for chunk
            Vector3 chunkPos = chunk->getWorldPosition();
            Matrix4 modelMatrix = Matrix4::translation(chunkPos);
            Matrix4 mvpMatrix = camera.getViewProjectionMatrix() * modelMatrix;
            
            // Update MVP matrix uniform
            m_shaderProgram->setUniformValue(m_mvpMatrixLocation, 
                QMatrix4x4(mvpMatrix.data()).transposed());
            
            // Render chunk mesh
            chunk->getMesh()->render();
        }
    }
    
    // Reset polygon mode
    glPolygonMode(GL_FRONT_AND_BACK, GL_FILL);
    
    // Unbind shader program
    m_shaderProgram->release();
}

bool Renderer::createShaders()
{
    m_shaderProgram = std::make_unique<QOpenGLShaderProgram>();
    
    // Compile vertex shader
    if (!m_shaderProgram->addShaderFromSourceCode(QOpenGLShader::Vertex, vertexShaderSource)) {
        qWarning() << "Failed to compile vertex shader:" << m_shaderProgram->log();
        return false;
    }
    
    // Compile fragment shader
    if (!m_shaderProgram->addShaderFromSourceCode(QOpenGLShader::Fragment, fragmentShaderSource)) {
        qWarning() << "Failed to compile fragment shader:" << m_shaderProgram->log();
        return false;
    }
    
    // Link shader program
    if (!m_shaderProgram->link()) {
        qWarning() << "Failed to link shader program:" << m_shaderProgram->log();
        return false;
    }
    
    // Get uniform locations
    m_mvpMatrixLocation = m_shaderProgram->uniformLocation("uMVPMatrix");
    m_textureLocation = m_shaderProgram->uniformLocation("uTexture");
    
    if (m_mvpMatrixLocation == -1 || m_textureLocation == -1) {
        qWarning() << "Failed to get uniform locations";
        return false;
    }
    
    // Bind texture sampler to texture unit 0
    m_shaderProgram->bind();
    m_shaderProgram->setUniformValue(m_textureLocation, 0);
    m_shaderProgram->release();
    
    qDebug() << "Shaders created successfully";
    return true;
}

void Renderer::setupUniforms(const Camera& /*camera*/)
{
    // The MVP matrix will be set per chunk in the render loop
    // Here we could set global uniforms like lighting, fog, etc.
}
