#include "Game.h"
#include "World.h"
#include "graphics/Camera.h"
#include "graphics/Renderer.h"
#include <QDebug>

Game::Game()
    : m_seed(0)
    , m_initialized(false)
{
}

Game::~Game()
{
    shutdown();
}

bool Game::initialize(const QString& worldName, int seed)
{
    if (m_initialized) {
        shutdown();
    }
    
    m_worldName = worldName;
    m_seed = seed;
    
    // Create world
    m_world = std::make_unique<World>(seed);
    if (!m_world) {
        qWarning() << "Failed to create world";
        return false;
    }
    
    // Create camera
    m_camera = std::make_unique<Camera>();
    if (!m_camera) {
        qWarning() << "Failed to create camera";
        return false;
    }
    
    // Set initial camera position (above ground)
    m_camera->setPosition(Vector3(0, 70, 0));
    m_camera->setPerspective(45.0f, 16.0f / 9.0f, 0.1f, 1000.0f);
    
    // Create renderer
    m_renderer = std::make_unique<Renderer>();
    if (!m_renderer) {
        qWarning() << "Failed to create renderer";
        return false;
    }
    
    // Initialize renderer
    if (!m_renderer->initialize()) {
        qWarning() << "Failed to initialize renderer";
        return false;
    }
    
    // Generate initial world chunks around spawn
    m_world->generateAroundPosition(m_camera->getPosition(), 8);
    
    m_initialized = true;
    qDebug() << "Game initialized successfully";
    qDebug() << "World:" << m_worldName << "Seed:" << m_seed;
    
    return true;
}

void Game::shutdown()
{
    if (m_initialized) {
        m_renderer.reset();
        m_camera.reset();
        m_world.reset();
        
        m_initialized = false;
        qDebug() << "Game shutdown";
    }
}

void Game::update(float deltaTime)
{
    if (!m_initialized) {
        return;
    }
    
    // Update camera
    m_camera->update(deltaTime);
    
    // Update world (chunk loading/unloading, mesh generation)
    m_world->update(m_camera->getPosition());
}

void Game::setRenderDistance(float distance)
{
    if (m_renderer) {
        m_renderer->setRenderDistance(distance);
    }
}

float Game::getRenderDistance() const
{
    if (m_renderer) {
        return m_renderer->getRenderDistance();
    }
    return 128.0f;
}

void Game::setWireframeMode(bool enabled)
{
    if (m_renderer) {
        m_renderer->setWireframeMode(enabled);
    }
}

bool Game::isWireframeMode() const
{
    if (m_renderer) {
        return m_renderer->isWireframeMode();
    }
    return false;
}
