@echo off
echo ========================================
echo Building CLEAN version (No MOC issues)
echo ========================================

echo.
echo Step 1: Complete cleanup...
call clean_build.bat

echo.
echo Step 2: Remove any MOC files...
del /q moc_*.cpp >nul 2>&1
del /q moc_*.h >nul 2>&1
del /q *.moc >nul 2>&1

echo.
echo Step 3: Building with MinecraftClone_Clean.pro...
echo This version excludes ALL files with Q_OBJECT macros.

qmake MinecraftClone_Clean.pro
if errorlevel 1 (
    echo ERROR: qmake failed!
    echo.
    echo Possible issues:
    echo - Qt not installed
    echo - Wrong Qt version (need 6.x)
    echo - PATH issues
    echo.
    pause
    exit /b 1
)

echo.
echo Step 4: Compiling...
nmake >nul 2>&1
if errorlevel 1 (
    echo nmake failed, trying make...
    make
    if errorlevel 1 (
        echo.
        echo BUILD FAILED!
        echo.
        echo Try opening Qt Creator and using Minecraft<PERSON>lone_Clean.pro
        echo This version has NO Q_OBJECT macros so MOC should not be needed.
        echo.
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo BUILD SUCCESSFUL!
echo ========================================
echo.
echo Executable: MinecraftClone_Clean.exe
echo.
echo This version:
echo ✓ No Q_OBJECT macros
echo ✓ No MOC compilation needed
echo ✓ Same game functionality
echo ✓ Full 3D world with terrain generation
echo ✓ WASD controls and mouse look
echo.
echo Run the game and enjoy!
echo.
pause
