#pragma once

#include "Block.h"
#include "math/Vector3.h"
#include <vector>
#include <memory>

class Mesh;

class Chunk
{
public:
    static const int CHUNK_SIZE = 16;
    static const int CHUNK_HEIGHT = 256;
    
    Chunk(int x, int z);
    ~Chunk();
    
    // Block access
    Block getBlock(int x, int y, int z) const;
    void setBlock(int x, int y, int z, const Block& block);
    
    // Position
    int getX() const { return m_x; }
    int getZ() const { return m_z; }
    Vector3 getWorldPosition() const { return Vector3(m_x * CHUNK_SIZE, 0, m_z * CHUNK_SIZE); }
    
    // Mesh generation
    void generateMesh();
    void rebuildMesh() { m_needsRebuild = true; }
    bool needsRebuild() const { return m_needsRebuild; }
    
    // Rendering
    const Mesh* getMesh() const { return m_mesh.get(); }
    
    // Utility
    bool isInBounds(int x, int y, int z) const;
    int getBlockIndex(int x, int y, int z) const;
    
    // Generation state
    bool isGenerated() const { return m_generated; }
    void setGenerated(bool generated) { m_generated = generated; }
    
    // Neighbor access (for mesh generation)
    void setNeighbor(int direction, Chunk* neighbor);
    Chunk* getNeighbor(int direction) const;
    
    enum NeighborDirection
    {
        NORTH = 0,  // -Z
        SOUTH = 1,  // +Z
        EAST = 2,   // +X
        WEST = 3    // -X
    };
    
private:
    void addFace(std::vector<float>& vertices, std::vector<unsigned int>& indices,
                 const Vector3& pos, int face, const Block::TextureCoords& texCoords);
    
    Block getBlockSafe(int x, int y, int z) const;
    
    int m_x, m_z;
    std::vector<Block> m_blocks;
    std::unique_ptr<Mesh> m_mesh;
    bool m_needsRebuild;
    bool m_generated;
    
    // Neighbor chunks for mesh generation
    Chunk* m_neighbors[4];
};
