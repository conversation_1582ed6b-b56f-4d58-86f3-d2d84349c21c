#include "GameWidget.h"
#include "core/World.h"
#include "graphics/Camera.h"
#include "graphics/Renderer.h"
#include <QApplication>
#include <cstring>

GameWidget::GameWidget(const QString& worldName, int seed, QWidget* parent)
    : QOpenGLWidget(parent)
    , m_worldName(worldName)
    , m_seed(seed)
    , m_mouseCaptured(false)
    , m_cameraSpeed(10.0f)
    , m_mouseSensitivity(0.1f)
    , m_deltaTime(0.0f)
{
    setFocusPolicy(Qt::StrongFocus);
    setMouseTracking(true);
    
    // Initialize input state
    std::memset(m_keys, 0, sizeof(m_keys));
    
    // Setup update timer
    m_updateTimer = new QTimer(this);
    connect(m_updateTimer, &QTimer::timeout, this, QOverload<>::of(&GameWidget::update));
    m_updateTimer->start(16); // ~60 FPS
    
    m_frameTimer.start();
    m_lastFrameTime = 0;
}

GameWidget::~GameWidget()
{
}

void GameWidget::initializeGL()
{
    initializeOpenGLFunctions();
    
    // Enable depth testing
    glEnable(GL_DEPTH_TEST);
    glDepthFunc(GL_LESS);
    
    // Enable face culling
    glEnable(GL_CULL_FACE);
    glCullFace(GL_BACK);
    glFrontFace(GL_CCW);
    
    // Set clear color (sky blue)
    glClearColor(0.5f, 0.8f, 1.0f, 1.0f);
    
    // Create world, camera, and renderer
    m_world = std::make_unique<World>(m_seed);
    m_camera = std::make_unique<Camera>();
    m_renderer = std::make_unique<Renderer>();
    
    // Initialize camera position
    m_camera->setPosition(Vector3(0, 70, 0));
    m_camera->setRotation(Vector3(0, 0, 0));
    
    // Initialize renderer
    m_renderer->initialize();
    
    // Generate initial world chunks
    m_world->generateAroundPosition(m_camera->getPosition());
}

void GameWidget::paintGL()
{
    // Calculate delta time
    qint64 currentTime = m_frameTimer.elapsed();
    m_deltaTime = (currentTime - m_lastFrameTime) / 1000.0f;
    m_lastFrameTime = currentTime;
    
    // Handle input and update camera
    handleInput(m_deltaTime);
    updateCamera(m_deltaTime);
    
    // Clear buffers
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
    
    // Update world around camera
    m_world->update(m_camera->getPosition());
    
    // Render world
    m_renderer->render(*m_world, *m_camera, width(), height());
}

void GameWidget::resizeGL(int width, int height)
{
    glViewport(0, 0, width, height);
    if (m_camera) {
        m_camera->setAspectRatio(static_cast<float>(width) / static_cast<float>(height));
    }
}

void GameWidget::keyPressEvent(QKeyEvent* event)
{
    if (event->key() < 256) {
        m_keys[event->key()] = true;
    }
    
    // Handle special keys
    if (event->key() == Qt::Key_Escape) {
        m_mouseCaptured = false;
        setCursor(Qt::ArrowCursor);
    }
}

void GameWidget::keyReleaseEvent(QKeyEvent* event)
{
    if (event->key() < 256) {
        m_keys[event->key()] = false;
    }
}

void GameWidget::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        m_mouseCaptured = true;
        setCursor(Qt::BlankCursor);
        m_lastMousePos = event->pos();
    }
}

void GameWidget::mouseMoveEvent(QMouseEvent* event)
{
    if (m_mouseCaptured) {
        QPoint delta = event->pos() - m_lastMousePos;
        
        // Update camera rotation
        Vector3 rotation = m_camera->getRotation();
        rotation.y -= delta.x() * m_mouseSensitivity;
        rotation.x -= delta.y() * m_mouseSensitivity;
        
        // Clamp pitch
        rotation.x = std::max(-89.0f, std::min(89.0f, rotation.x));
        
        m_camera->setRotation(rotation);
        
        // Reset mouse to center of widget
        QPoint center = rect().center();
        QCursor::setPos(mapToGlobal(center));
        m_lastMousePos = center;
    }
}

void GameWidget::wheelEvent(QWheelEvent* event)
{
    // Adjust movement speed with mouse wheel
    float speedChange = event->angleDelta().y() / 120.0f;
    m_cameraSpeed = std::max(1.0f, std::min(50.0f, m_cameraSpeed + speedChange));
}

void GameWidget::updateCamera(float deltaTime)
{
    m_camera->update(deltaTime);
}

void GameWidget::handleInput(float deltaTime)
{
    if (!m_camera) return;
    
    Vector3 movement = Vector3::zero();
    
    // WASD movement
    if (m_keys[Qt::Key_W]) movement += Vector3::forward();
    if (m_keys[Qt::Key_S]) movement -= Vector3::forward();
    if (m_keys[Qt::Key_A]) movement -= Vector3::right();
    if (m_keys[Qt::Key_D]) movement += Vector3::right();
    
    // Vertical movement
    if (m_keys[Qt::Key_Space]) movement += Vector3::up();
    if (m_keys[Qt::Key_Shift]) movement -= Vector3::up();
    
    // Apply movement
    if (movement.lengthSquared() > 0) {
        movement.normalize();
        movement *= m_cameraSpeed * deltaTime;
        
        // Transform movement relative to camera orientation
        Vector3 forward = m_camera->getForward();
        Vector3 right = m_camera->getRight();
        Vector3 up = Vector3::up();
        
        Vector3 worldMovement = 
            right * movement.x + 
            up * movement.y + 
            forward * movement.z;
        
        m_camera->move(worldMovement);
    }
}

#include "GameWidget.moc"
