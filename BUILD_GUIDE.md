# Complete Build Guide - Minecraft Clone

## 🚀 Quick Start Options

### Option 1: Qt Creator (Recommended)
1. **Install Qt 6.x** with Qt Creator from https://www.qt.io/download
2. **Open Qt Creator**
3. **File → Open File or Project**
4. **Select `MinecraftClone_Simple.pro`** (use this version for better compatibility)
5. **Configure project** with your Qt kit
6. **Build and Run** (Ctrl+R)

### Option 2: Test Components First
```bash
# Test core math/logic
qmake test_core.pro && make && ./test_core

# Test OpenGL mesh system
qmake test_mesh.pro && make && ./test_mesh

# Then build main game
qmake MinecraftClone_Simple.pro && make
```

### Option 3: Clean Build
```bash
# Clean everything first
clean_build.bat  # Windows
# or manually delete: *.o, *.obj, Makefile*, moc_*.cpp, qrc_*.cpp

# Build
qmake MinecraftClone_Simple.pro
make  # or nmake on Windows with Visual Studio
```

## 🔧 Troubleshooting Recent Issues

### Issue 1: MOC File Errors
**Error:** `No rule to make target 'moc/MainWindow.moc'`

**Solutions:**
1. Use `MinecraftClone_Simple.pro` (no custom build directories)
2. Clean build completely: `clean_build.bat`
3. In Qt Creator: Build → Clean All → Run qmake → Build

### Issue 2: OpenGL Const Errors
**Error:** `passing 'const Mesh' as 'this' argument discards qualifiers`

**Status:** ✅ **FIXED** - Now uses `QOpenGLContext::currentContext()->extraFunctions()`

### Issue 3: Missing OpenGL Functions
**Error:** `'glGenVertexArrays' was not declared`

**Status:** ✅ **FIXED** - Uses `QOpenGLExtraFunctions`

## 📁 Project Files

### Main Project Files:
- `MinecraftClone_Simple.pro` - **Use this for building** (simplified)
- `MinecraftClone.pro` - Original (may have path issues)

### Test Files:
- `test_core.pro` - Test math and noise systems
- `test_mesh.pro` - Test OpenGL mesh rendering

### Build Scripts:
- `clean_build.bat` - Clean all build files
- `build_qt.bat` - Automated build

## 🎯 Step-by-Step Build Process

### Step 1: Verify Qt Installation
```bash
qmake --version
# Should show Qt 6.x
```

### Step 2: Clean Previous Builds
```bash
clean_build.bat  # Windows
# or manually remove build files
```

### Step 3: Test Core Components
```bash
qmake test_core.pro
make
./test_core
# Should show: "All core tests completed successfully!"
```

### Step 4: Build Main Game
```bash
qmake MinecraftClone_Simple.pro
make
# Creates MinecraftClone.exe
```

### Step 5: Run Game
```bash
./MinecraftClone  # or MinecraftClone.exe on Windows
```

## 🖥️ Platform-Specific Notes

### Windows:
- Use Qt Creator or MinGW/Visual Studio
- May need `nmake` instead of `make`
- Ensure Qt bin directory is in PATH

### Linux:
- Install Qt development packages
- May need additional OpenGL libraries
- Use standard `make`

### macOS:
- Install Qt from official installer
- Use Xcode command line tools
- Standard `make` should work

## 🎮 Expected Results

### Successful Build:
1. **No compilation errors**
2. **Executable created** (MinecraftClone.exe)
3. **Game window opens**
4. **3D world generates**
5. **WASD controls work**

### If Build Fails:
1. Check Qt version (must be 6.x)
2. Try `MinecraftClone_Simple.pro`
3. Clean build completely
4. Test components individually
5. Check TROUBLESHOOTING.md

## 🔍 Verification Steps

### Test 1: Core Systems
```bash
qmake test_core.pro && make && ./test_core
```
**Expected:** Math and noise tests pass

### Test 2: OpenGL Mesh
```bash
qmake test_mesh.pro && make && ./test_mesh
```
**Expected:** OpenGL window opens, no crashes

### Test 3: Full Game
```bash
qmake MinecraftClone_Simple.pro && make && ./MinecraftClone
```
**Expected:** Game starts, world generates

## 📞 Getting Help

1. **Check this guide** - Most issues covered here
2. **Use simple .pro file** - `MinecraftClone_Simple.pro`
3. **Test components** - Start with `test_core.pro`
4. **Clean build** - Use `clean_build.bat`
5. **Check Qt version** - Must be 6.x

## ✅ Success Checklist

- [ ] Qt 6.x installed
- [ ] `qmake --version` works
- [ ] `test_core.pro` builds and runs
- [ ] `MinecraftClone_Simple.pro` configures
- [ ] Build completes without errors
- [ ] Game executable created
- [ ] Game window opens
- [ ] 3D world visible
- [ ] WASD movement works

If all items check out, you have a working Minecraft clone! 🎮
