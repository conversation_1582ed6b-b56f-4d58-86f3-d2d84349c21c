#pragma once

#include <vector>
#include <random>

class NoiseGenerator
{
public:
    explicit NoiseGenerator(int seed);
    
    // 2D Perlin noise
    float noise2D(float x, float y) const;
    float octaveNoise2D(float x, float y, int octaves, float persistence = 0.5f, float scale = 1.0f) const;
    
    // 3D Perlin noise
    float noise3D(float x, float y, float z) const;
    float octaveNoise3D(float x, float y, float z, int octaves, float persistence = 0.5f, float scale = 1.0f) const;
    
    // Utility functions
    float ridgedNoise2D(float x, float y, int octaves = 4, float scale = 1.0f) const;
    float billowNoise2D(float x, float y, int octaves = 4, float scale = 1.0f) const;
    
private:
    // Permutation table for Perlin noise
    std::vector<int> m_permutation;
    
    // Helper functions
    float fade(float t) const;
    float lerp(float t, float a, float b) const;
    float grad(int hash, float x, float y) const;
    float grad(int hash, float x, float y, float z) const;
    
    void initializePermutation(int seed);
};
