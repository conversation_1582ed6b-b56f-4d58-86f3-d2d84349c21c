#pragma once

#include <cstdint>

enum class BlockType : uint8_t
{
    Air = 0,
    Grass,
    Dirt,
    Stone,
    Wood,
    Leaves,
    Sand,
    Water,
    Bedrock,
    COUNT
};

class Block
{
public:
    Block() : m_type(BlockType::Air) {}
    Block(BlockType type) : m_type(type) {}
    
    BlockType getType() const { return m_type; }
    void setType(BlockType type) { m_type = type; }
    
    bool isAir() const { return m_type == BlockType::Air; }
    bool isSolid() const { return m_type != BlockType::Air && m_type != BlockType::Water; }
    bool isTransparent() const { return m_type == BlockType::Air || m_type == BlockType::Water || m_type == BlockType::Leaves; }
    
    // Get texture coordinates for each face
    struct TextureCoords
    {
        float u1, v1, u2, v2;
    };
    
    TextureCoords getTextureCoords(int face) const;
    
    // Face indices
    enum Face
    {
        FRONT = 0,
        BACK = 1,
        LEFT = 2,
        RIGHT = 3,
        TOP = 4,
        BOTTOM = 5
    };
    
    static const char* getBlockName(BlockType type);
    static bool shouldRenderFace(BlockType current, BlockType neighbor);
    
private:
    BlockType m_type;
};
