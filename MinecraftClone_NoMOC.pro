QT += core widgets opengl openglwidgets svg
CONFIG += c++17
TARGET = MinecraftClone_NoMOC
TEMPLATE = app

# No Q_OBJECT macros used - MOC should not be needed

SOURCES += \
    src/main_nomoc.cpp \
    src/core/Game.cpp \
    src/core/Block.cpp \
    src/core/Chunk.cpp \
    src/core/World.cpp \
    src/graphics/Renderer.cpp \
    src/graphics/Camera.cpp \
    src/graphics/Mesh.cpp \
    src/world/TerrainGenerator.cpp \
    src/world/NoiseGenerator.cpp \
    src/ui/BasicGameWidget.cpp \
    src/textures/TextureManager.cpp \
    src/textures/SvgTextureGenerator.cpp \
    src/math/Vector3.cpp \
    src/math/Matrix4.cpp

HEADERS += \
    src/core/Game.h \
    src/core/Block.h \
    src/core/Chunk.h \
    src/core/World.h \
    src/graphics/Renderer.h \
    src/graphics/Camera.h \
    src/graphics/Mesh.h \
    src/world/TerrainGenerator.h \
    src/world/NoiseGenerator.h \
    src/ui/BasicGameWidget.h \
    src/textures/TextureManager.h \
    src/textures/SvgTextureGenerator.h \
    src/math/Vector3.h \
    src/math/Matrix4.h

INCLUDEPATH += src
RESOURCES += resources.qrc

win32: LIBS += -lopengl32
unix: LIBS += -lGL
