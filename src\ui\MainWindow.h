#pragma once

#include <QMainWindow>
#include <QMenuBar>
#include <QStatusBar>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QDialog>
#include <QSpinBox>
#include <QLineEdit>

class GameWidget;

class NewWorldDialog : public QDialog
{
    Q_OBJECT
    
public:
    explicit NewWorldDialog(QWidget* parent = nullptr);
    
    QString getWorldName() const;
    int getSeed() const;
    
private:
    QLineEdit* m_worldNameEdit;
    QSpinBox* m_seedSpinBox;
};

class MainWindow : public QMainWindow
{
    Q_OBJECT
    
public:
    explicit MainWindow(QWidget* parent = nullptr);
    ~MainWindow();
    
private slots:
    void newWorld();
    void loadWorld();
    void saveWorld();
    void exitGame();
    void showAbout();
    
private:
    void setupMenus();
    void setupUI();
    void startGame(const QString& worldName, int seed);
    
    GameWidget* m_gameWidget;
    QWidget* m_centralWidget;
    QVBoxLayout* m_mainLayout;
    
    // Menu items
    QAction* m_newWorldAction;
    QAction* m_loadWorldAction;
    QAction* m_saveWorldAction;
    QAction* m_exitAction;
    QAction* m_aboutAction;
    
    bool m_gameStarted;
};
