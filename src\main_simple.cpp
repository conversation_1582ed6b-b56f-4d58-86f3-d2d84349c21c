#include <QApplication>
#include <QSurfaceFormat>
#include "ui/SimpleMainWindow.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // Set up OpenGL format
    QSurfaceFormat format;
    format.setDepthBufferSize(24);
    format.setStencilBufferSize(8);
    format.setVersion(3, 3);
    format.setProfile(QSurfaceFormat::CoreProfile);
    format.setSamples(4); // Anti-aliasing
    QSurfaceFormat::setDefaultFormat(format);
    
    // Create and show main window
    SimpleMainWindow window;
    window.show();
    
    return app.exec();
}
