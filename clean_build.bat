@echo off
echo Cleaning build files...

REM Remove build artifacts
del /q *.o >nul 2>&1
del /q *.obj >nul 2>&1
del /q Makefile* >nul 2>&1
del /q *.exe >nul 2>&1
del /q moc_*.cpp >nul 2>&1
del /q qrc_*.cpp >nul 2>&1

REM Remove directories
rmdir /s /q obj >nul 2>&1
rmdir /s /q moc >nul 2>&1
rmdir /s /q rcc >nul 2>&1
rmdir /s /q ui >nul 2>&1
rmdir /s /q bin >nul 2>&1
rmdir /s /q build >nul 2>&1
rmdir /s /q debug >nul 2>&1
rmdir /s /q release >nul 2>&1

echo Clean complete!
echo.
echo Now run: qmake MinecraftClone.pro && make
pause
