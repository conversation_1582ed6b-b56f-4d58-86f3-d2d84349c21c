#include "TextureManager.h"
#include "SvgTextureGenerator.h"
#include <QOpenGLContext>
#include <QDebug>

TextureManager::TextureManager()
    : m_svgGenerator(std::make_unique<SvgTextureGenerator>())
    , m_textureAtlasId(0)
    , m_textureSize(64)
    , m_atlasSize(4)
    , m_initialized(false)
{
}

TextureManager::~TextureManager()
{
    cleanup();
}

bool TextureManager::initialize()
{
    if (m_initialized) {
        return true;
    }
    
    if (!QOpenGLContext::currentContext()) {
        qWarning() << "No OpenGL context available for TextureManager";
        return false;
    }
    
    initializeOpenGLFunctions();
    generateTextures();
    
    m_initialized = true;
    return true;
}

void TextureManager::cleanup()
{
    if (m_initialized && QOpenGLContext::currentContext()) {
        if (m_textureAtlasId != 0) {
            glDeleteTextures(1, &m_textureAtlasId);
            m_textureAtlasId = 0;
        }
    }
    
    m_initialized = false;
}

void TextureManager::bindTextureAtlas()
{
    if (m_textureAtlasId != 0) {
        glActiveTexture(GL_TEXTURE0);
        glBindTexture(GL_TEXTURE_2D, m_textureAtlasId);
    }
}

void TextureManager::generateTextures()
{
    createTextureAtlas();
}

void TextureManager::reloadTextures()
{
    if (m_textureAtlasId != 0) {
        glDeleteTextures(1, &m_textureAtlasId);
        m_textureAtlasId = 0;
    }
    
    generateTextures();
}

void TextureManager::createTextureAtlas()
{
    // Generate texture atlas using SVG generator
    QPixmap atlas = m_svgGenerator->generateTextureAtlas(m_textureSize, m_atlasSize);
    
    if (atlas.isNull()) {
        qWarning() << "Failed to generate texture atlas";
        return;
    }
    
    // Create OpenGL texture
    m_textureAtlasId = createOpenGLTexture(atlas);
    
    if (m_textureAtlasId == 0) {
        qWarning() << "Failed to create OpenGL texture atlas";
    } else {
        qDebug() << "Texture atlas created successfully with ID:" << m_textureAtlasId;
    }
}

unsigned int TextureManager::createOpenGLTexture(const QPixmap& pixmap)
{
    if (pixmap.isNull()) {
        return 0;
    }
    
    // Convert pixmap to OpenGL-compatible format
    QImage image = pixmap.toImage();
    image = image.convertToFormat(QImage::Format_RGBA8888);
    
    unsigned int textureId;
    glGenTextures(1, &textureId);
    glBindTexture(GL_TEXTURE_2D, textureId);
    
    // Upload texture data
    glTexImage2D(GL_TEXTURE_2D, 0, GL_RGBA, image.width(), image.height(), 
                 0, GL_RGBA, GL_UNSIGNED_BYTE, image.constBits());
    
    // Set texture parameters
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_NEAREST);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_NEAREST);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_REPEAT);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_REPEAT);
    
    // Generate mipmaps
    glGenerateMipmap(GL_TEXTURE_2D);
    
    // Unbind texture
    glBindTexture(GL_TEXTURE_2D, 0);
    
    return textureId;
}
