#include "Camera.h"
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

Camera::Camera()
    : m_position(0, 0, 0)
    , m_rotation(0, 0, 0)
    , m_fov(45.0f)
    , m_aspectRatio(16.0f / 9.0f)
    , m_nearPlane(0.1f)
    , m_farPlane(1000.0f)
    , m_directionsDirty(true)
{
    updateViewMatrix();
    updateProjectionMatrix();
}

Camera::~Camera()
{
}

Vector3 Camera::getForward() const
{
    if (m_directionsDirty) {
        float yawRad = m_rotation.y * M_PI / 180.0f;
        float pitchRad = m_rotation.x * M_PI / 180.0f;
        
        m_forward.x = std::cos(pitchRad) * std::sin(yawRad);
        m_forward.y = -std::sin(pitchRad);
        m_forward.z = -std::cos(pitchRad) * std::cos(yawRad);
        m_forward.normalize();
        
        m_right = Vector3::up().cross(m_forward).normalized();
        m_up = m_forward.cross(m_right).normalized();
        
        m_directionsDirty = false;
    }
    
    return m_forward;
}

Vector3 Camera::getRight() const
{
    if (m_directionsDirty) {
        getForward(); // This will update all direction vectors
    }
    return m_right;
}

Vector3 Camera::getUp() const
{
    if (m_directionsDirty) {
        getForward(); // This will update all direction vectors
    }
    return m_up;
}

void Camera::setPerspective(float fov, float aspectRatio, float nearPlane, float farPlane)
{
    m_fov = fov;
    m_aspectRatio = aspectRatio;
    m_nearPlane = nearPlane;
    m_farPlane = farPlane;
    updateProjectionMatrix();
}

void Camera::setAspectRatio(float aspectRatio)
{
    m_aspectRatio = aspectRatio;
    updateProjectionMatrix();
}

void Camera::update(float /*deltaTime*/)
{
    // Update any camera-specific logic here
    // For now, just ensure matrices are up to date
}

void Camera::updateViewMatrix()
{
    Vector3 forward = getForward();
    Vector3 target = m_position + forward;
    Vector3 up = Vector3::up();
    
    m_viewMatrix = Matrix4::lookAt(m_position, target, up);
    m_directionsDirty = true;
}

void Camera::updateProjectionMatrix()
{
    float fovRad = m_fov * M_PI / 180.0f;
    m_projectionMatrix = Matrix4::perspective(fovRad, m_aspectRatio, m_nearPlane, m_farPlane);
}
