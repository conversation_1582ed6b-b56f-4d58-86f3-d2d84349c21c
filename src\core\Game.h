#pragma once

#include <memory>
#include <QString>

class World;
class Camera;
class Renderer;

class Game
{
public:
    Game();
    ~Game();
    
    // Game lifecycle
    bool initialize(const QString& worldName, int seed);
    void shutdown();
    void update(float deltaTime);
    
    // Accessors
    World* getWorld() const { return m_world.get(); }
    Camera* getCamera() const { return m_camera.get(); }
    Renderer* getRenderer() const { return m_renderer.get(); }
    
    // Game state
    bool isInitialized() const { return m_initialized; }
    const QString& getWorldName() const { return m_worldName; }
    int getSeed() const { return m_seed; }
    
    // Game settings
    void setRenderDistance(float distance);
    float getRenderDistance() const;
    
    void setWireframeMode(bool enabled);
    bool isWireframeMode() const;
    
private:
    std::unique_ptr<World> m_world;
    std::unique_ptr<Camera> m_camera;
    std::unique_ptr<Renderer> m_renderer;
    
    QString m_worldName;
    int m_seed;
    bool m_initialized;
};
