#include <QApplication>
#include <QMainWindow>
#include <QSurfaceFormat>
#include <QVBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QRandomGenerator>
#include "ui/BasicGameWidget.h"

class SimpleWindow : public QMainWindow
{
    // No Q_OBJECT - no MOC needed
public:
    SimpleWindow() : m_gameWidget(nullptr), m_gameStarted(false)
    {
        setWindowTitle("Minecraft Clone - No MOC Version");
        setMinimumSize(800, 600);
        setupUI();
    }
    
private:
    void setupUI()
    {
        auto* centralWidget = new QWidget;
        setCentralWidget(centralWidget);
        
        m_mainLayout = new QVBoxLayout(centralWidget);
        
        // Welcome screen
        auto* welcomeLabel = new QLabel("Minecraft Clone - No MOC Version");
        welcomeLabel->setAlignment(Qt::AlignCenter);
        welcomeLabel->setStyleSheet("font-size: 24px; font-weight: bold; margin: 50px;");
        m_mainLayout->addWidget(welcomeLabel);
        
        auto* startButton = new QPushButton("Start Game");
        startButton->setMinimumHeight(40);
        
        // Use lambda instead of signal/slot to avoid MOC
        QObject::connect(startButton, &QPushButton::clicked, [this]() {
            startGame();
        });
        
        m_mainLayout->addWidget(startButton);
        
        statusBar()->showMessage("Click 'Start Game' to begin - No MOC version");
    }
    
    void startGame()
    {
        if (m_gameStarted) {
            return;
        }
        
        // Clear existing layout
        QLayoutItem* item;
        while ((item = m_mainLayout->takeAt(0)) != nullptr) {
            delete item->widget();
            delete item;
        }
        
        // Create game widget with default world
        QString worldName = "Test World";
        int seed = QRandomGenerator::global()->bounded(INT_MAX);
        
        m_gameWidget = new BasicGameWidget(worldName, seed);
        m_mainLayout->addWidget(m_gameWidget);
        
        m_gameStarted = true;
        statusBar()->showMessage(QString("Playing world: %1 (Seed: %2)").arg(worldName).arg(seed));
    }
    
    BasicGameWidget* m_gameWidget;
    QVBoxLayout* m_mainLayout;
    bool m_gameStarted;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // Set up OpenGL format
    QSurfaceFormat format;
    format.setDepthBufferSize(24);
    format.setStencilBufferSize(8);
    format.setVersion(3, 3);
    format.setProfile(QSurfaceFormat::CoreProfile);
    format.setSamples(4);
    QSurfaceFormat::setDefaultFormat(format);
    
    // Create and show main window
    SimpleWindow window;
    window.show();
    
    return app.exec();
}
