QT += core widgets opengl openglwidgets svg

CONFIG += c++17

TARGET = MinecraftClone
TEMPLATE = app

# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

# Include directories
INCLUDEPATH += src \
               src/core \
               src/graphics \
               src/world \
               src/ui \
               src/textures \
               src/math

# Source files
SOURCES += \
    src/main.cpp \
    src/core/Game.cpp \
    src/core/Block.cpp \
    src/core/Chunk.cpp \
    src/core/World.cpp \
    src/graphics/Renderer.cpp \
    src/graphics/Camera.cpp \
    src/graphics/Mesh.cpp \
    src/world/TerrainGenerator.cpp \
    src/world/NoiseGenerator.cpp \
    src/ui/MainWindow.cpp \
    src/ui/GameWidget.cpp \
    src/textures/TextureManager.cpp \
    src/textures/SvgTextureGenerator.cpp \
    src/math/Vector3.cpp \
    src/math/Matrix4.cpp

# Header files
HEADERS += \
    src/core/Game.h \
    src/core/Block.h \
    src/core/Chunk.h \
    src/core/World.h \
    src/graphics/Renderer.h \
    src/graphics/Camera.h \
    src/graphics/Mesh.h \
    src/world/TerrainGenerator.h \
    src/world/NoiseGenerator.h \
    src/ui/MainWindow.h \
    src/ui/GameWidget.h \
    src/textures/TextureManager.h \
    src/textures/SvgTextureGenerator.h \
    src/math/Vector3.h \
    src/math/Matrix4.h

# Resources
RESOURCES += resources.qrc

# OpenGL linking
win32 {
    LIBS += -lopengl32
}

unix {
    LIBS += -lGL
}

# Output directory
DESTDIR = bin
OBJECTS_DIR = obj
MOC_DIR = moc
RCC_DIR = rcc
UI_DIR = ui

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
