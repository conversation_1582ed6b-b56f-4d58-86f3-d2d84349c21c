#pragma once

#include <QMainWindow>
#include <QVBoxLayout>
#include <QPushButton>
#include <QLabel>

class GameWidget;

class SimpleMainWindow : public QMainWindow
{
    Q_OBJECT
    
public:
    explicit SimpleMainWindow(QWidget* parent = nullptr);
    ~SimpleMainWindow();
    
private slots:
    void startGame();
    
private:
    void setupUI();
    
    GameWidget* m_gameWidget;
    QWidget* m_centralWidget;
    QVBoxLayout* m_mainLayout;
    bool m_gameStarted;
};
