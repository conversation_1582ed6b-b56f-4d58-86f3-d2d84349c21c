# Minecraft Clone

A simple voxel-based game built with Qt C++ and OpenGL, featuring random terrain generation and SVG-based textures.

## Features

- **3D Voxel World**: Infinite procedurally generated world with chunks
- **Random Terrain Generation**: Perlin noise-based terrain with different biomes
- **SVG Textures**: Procedurally generated textures using SVG for crisp, scalable graphics
- **First-Person Camera**: WASD movement with mouse look
- **Multiple Block Types**: Grass, dirt, stone, wood, leaves, sand, water, and bedrock
- **Chunk System**: Efficient world loading and unloading
- **OpenGL Rendering**: Hardware-accelerated 3D graphics

## Requirements

- Qt 6.x
- CMake 3.16+
- C++17 compatible compiler
- OpenGL 3.3+ support

## Building

### Qt Creator (Recommended)
1. Install Qt 6.x with Qt Creator
2. Open `MinecraftClone.pro` in Qt Creator
3. Configure project with your Qt kit
4. Build and Run (Ctrl+R)

### Command Line
```bash
# Windows
build_qt.bat

# Or manually:
qmake MinecraftClone.pro
make  # or nmake on Windows with Visual Studio
```

### Clean Build
```bash
clean_build.bat  # Windows
# Then rebuild
```

### Test Core Components First
```bash
qmake test_core.pro
make
./test_core  # Verify basic functionality
```

## Controls

- **WASD**: Move around
- **Mouse**: Look around (click to capture mouse)
- **Space**: Move up
- **Shift**: Move down
- **Mouse Wheel**: Adjust movement speed
- **Escape**: Release mouse cursor

## World Generation

The game uses multiple layers of Perlin noise to generate:
- **Height maps** for terrain elevation
- **Temperature and humidity** maps for biome selection
- **Cave systems** using 3D noise
- **Tree placement** based on biome type

## Biomes

- **Plains**: Grass blocks with occasional trees
- **Forest**: Dense tree coverage
- **Desert**: Sand blocks
- **Mountains**: High elevation stone terrain
- **Ocean**: Water-filled low areas

## Architecture

- **Core**: Game logic, world management, block system
- **Graphics**: OpenGL rendering, camera, mesh management
- **World**: Terrain generation, chunk system
- **Textures**: SVG-based texture generation and atlas management
- **UI**: Qt-based user interface and game window

## Texture System

Textures are generated procedurally using SVG, providing:
- Scalable graphics that look crisp at any resolution
- Programmatic texture generation with noise patterns
- Easy customization and modification
- Efficient texture atlas packing

## Performance

- Chunk-based world loading for infinite worlds
- Frustum culling for efficient rendering
- Mesh optimization with face culling
- Configurable render distance

## Future Enhancements

- Block placement/destruction
- Inventory system
- Lighting system
- Water physics
- Multiplayer support
- Save/load functionality
- More biomes and block types
