#pragma once

#include "NoiseGenerator.h"
#include "core/Block.h"
#include <memory>

class Chunk;

class TerrainGenerator
{
public:
    explicit TerrainGenerator(int seed);
    ~TerrainGenerator();
    
    void generateChunk(Chunk& chunk);
    
    // Terrain parameters
    struct TerrainSettings
    {
        int seaLevel = 64;
        int maxHeight = 128;
        int minHeight = 32;
        
        // Noise scales
        float heightScale = 0.01f;
        float caveScale = 0.05f;
        float oreScale = 0.1f;
        
        // Generation parameters
        int heightOctaves = 6;
        int caveOctaves = 3;
        float caveDensity = 0.4f;
        
        // Biome parameters
        float temperatureScale = 0.005f;
        float humidityScale = 0.005f;
    };
    
    const TerrainSettings& getSettings() const { return m_settings; }
    void setSettings(const TerrainSettings& settings) { m_settings = settings; }
    
private:
    enum class BiomeType
    {
        Plains,
        Forest,
        Desert,
        Mountains,
        Ocean
    };
    
    BiomeType getBiome(float temperature, float humidity) const;
    BlockType getBlockForBiome(BiomeType biome, int height, int surfaceHeight, bool isUnderground) const;
    
    void generateTerrain(Chunk& chunk);
    void generateCaves(Chunk& chunk);
    void generateOres(Chunk& chunk);
    void generateTrees(Chunk& chunk);
    
    int getHeightAt(int worldX, int worldZ) const;
    float getTemperatureAt(int worldX, int worldZ) const;
    float getHumidityAt(int worldX, int worldZ) const;
    
    int m_seed;
    std::unique_ptr<NoiseGenerator> m_heightNoise;
    std::unique_ptr<NoiseGenerator> m_caveNoise;
    std::unique_ptr<NoiseGenerator> m_oreNoise;
    std::unique_ptr<NoiseGenerator> m_temperatureNoise;
    std::unique_ptr<NoiseGenerator> m_humidityNoise;
    
    TerrainSettings m_settings;
};
