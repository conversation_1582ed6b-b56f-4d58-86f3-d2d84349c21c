# Troubleshooting Guide - Minecraft Clone

## Common Build Issues and Solutions

### 1. OpenGL Function Errors (glGenVertexArrays, etc.)

**Error:**
```
error: 'glGenVertexArrays' was not declared in this scope
```

**Solution:**
- Make sure you have Qt 6.x installed (not Qt 5.x)
- Ensure your graphics drivers support OpenGL 3.3+
- The code uses `QOpenGLExtraFunctions` which should provide these functions

### 2. MOC File Errors

**Error:**
```
error: No rule to make target 'build/moc/MainWindow.moc'
```

**Solution:**
```bash
# Clean and rebuild
clean_build.bat
qmake MinecraftClone.pro
make
```

### 3. Unused Parameter Warnings

**Status:** ✅ Fixed
- Added `/*parameter*/` comments to suppress warnings

### 4. Qt Creator Setup

**Recommended Steps:**
1. Install Qt 6.2+ with Qt Creator
2. Open Qt Creator
3. File → Open File or Project
4. Select `MinecraftClone.pro`
5. Configure with your Qt kit
6. Build and Run (Ctrl+R)

### 5. Command Line Build

**Windows (MinGW):**
```cmd
qmake MinecraftClone.pro
make
```

**Windows (Visual Studio):**
```cmd
qmake MinecraftClone.pro
nmake
```

**Linux/Mac:**
```bash
qmake MinecraftClone.pro
make
```

### 6. Missing Qt Modules

**Error:**
```
Project ERROR: Unknown module(s) in QT: openglwidgets
```

**Solution:**
- Install Qt 6.x (openglwidgets is Qt 6 only)
- Or modify .pro file to use Qt 5 compatible modules

### 7. OpenGL Context Issues

**Error:**
```
No OpenGL context available
```

**Solution:**
- Ensure graphics drivers are up to date
- Try running on a system with OpenGL 3.3+ support
- Check if hardware acceleration is enabled

## Alternative Build Methods

### Method 1: Qt Creator (Recommended)
- Most reliable method
- Automatic dependency handling
- Integrated debugging

### Method 2: Command Line
- Use provided batch files
- `build_qt.bat` for automated build
- `clean_build.bat` to clean first

### Method 3: CMake (Advanced)
```bash
mkdir build
cd build
cmake ..
cmake --build .
```

## Testing Core Components

Before building the full game, test core components:

```bash
# Test basic functionality
qmake test_core.pro
make
./test_core
```

## System Requirements

### Minimum:
- Qt 6.0+
- OpenGL 3.3+ support
- C++17 compiler
- 4GB RAM

### Recommended:
- Qt 6.2+
- Dedicated graphics card
- 8GB+ RAM
- SSD storage

## Common Runtime Issues

### 1. Black Screen
- Check OpenGL support
- Update graphics drivers
- Try windowed mode

### 2. Poor Performance
- Reduce render distance
- Check graphics settings
- Close other applications

### 3. Crash on Startup
- Check Qt installation
- Verify OpenGL support
- Run in debug mode for details

## Debug Mode

To get better error messages:

**Qt Creator:**
- Select Debug build configuration
- Run with debugger (F5)

**Command Line:**
```bash
qmake CONFIG+=debug MinecraftClone.pro
make
```

## Getting Help

1. Check this troubleshooting guide
2. Verify Qt installation
3. Test with `test_core.pro` first
4. Check system requirements
5. Try different build methods

## Success Indicators

✅ **Core test passes** - Basic math and noise work  
✅ **Project configures** - qmake succeeds  
✅ **Build completes** - No compilation errors  
✅ **Game starts** - Window opens  
✅ **World generates** - Terrain appears  
✅ **Controls work** - WASD movement  

If all indicators pass, the game should work perfectly!
