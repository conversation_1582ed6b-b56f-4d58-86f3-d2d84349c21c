@echo off
echo ========================================
echo Minecraft Clone - Try All Build Methods
echo ========================================

echo.
echo Cleaning previous builds...
call clean_build.bat

echo.
echo ========================================
echo Method 1: No MOC Version (Simplest)
echo ========================================
echo.
echo Building MinecraftClone_NoMOC.pro...
qmake MinecraftClone_NoMOC.pro
if errorlevel 1 (
    echo qmake failed for NoMOC version
    goto method2
)

nmake >nul 2>&1
if errorlevel 1 (
    make >nul 2>&1
    if errorlevel 1 (
        echo Build failed for NoMOC version
        goto method2
    )
)

echo ✓ SUCCESS: NoMOC version built!
echo Executable: MinecraftClone_NoMOC.exe
echo This version avoids all MOC issues.
goto success

:method2
echo.
echo ========================================
echo Method 2: Minimal Version
echo ========================================
echo.
call clean_build.bat
echo Building MinecraftClone_Minimal.pro...
qmake MinecraftClone_Minimal.pro
if errorlevel 1 (
    echo qmake failed for Minimal version
    goto method3
)

nmake >nul 2>&1
if errorlevel 1 (
    make >nul 2>&1
    if errorlevel 1 (
        echo Build failed for Minimal version
        goto method3
    )
)

echo ✓ SUCCESS: Minimal version built!
echo Executable: MinecraftClone.exe
goto success

:method3
echo.
echo ========================================
echo Method 3: Fixed Version
echo ========================================
echo.
call clean_build.bat
echo Building MinecraftClone_Fixed.pro...
qmake MinecraftClone_Fixed.pro
if errorlevel 1 (
    echo qmake failed for Fixed version
    goto method4
)

nmake >nul 2>&1
if errorlevel 1 (
    make >nul 2>&1
    if errorlevel 1 (
        echo Build failed for Fixed version
        goto method4
    )
)

echo ✓ SUCCESS: Fixed version built!
echo Executable: MinecraftClone.exe
goto success

:method4
echo.
echo ========================================
echo Method 4: Core Test Only
echo ========================================
echo.
call clean_build.bat
echo Building test_core.pro...
qmake test_core.pro
if errorlevel 1 (
    echo qmake failed for core test
    goto failed
)

nmake >nul 2>&1
if errorlevel 1 (
    make >nul 2>&1
    if errorlevel 1 (
        echo Build failed for core test
        goto failed
    )
)

echo ✓ SUCCESS: Core test built!
echo Running core test...
if exist "test_core.exe" (
    test_core.exe
) else if exist "test_core" (
    ./test_core
)
echo.
echo Core systems work! Try building in Qt Creator with one of these .pro files:
echo - MinecraftClone_NoMOC.pro (recommended)
echo - MinecraftClone_Minimal.pro
echo - MinecraftClone_Fixed.pro
goto end

:success
echo.
echo ========================================
echo BUILD SUCCESSFUL!
echo ========================================
echo.
echo The game should now be ready to run.
echo.
echo Controls:
echo - WASD: Move
echo - Mouse: Look around (click to capture)
echo - Space/Shift: Up/Down
echo - Escape: Release mouse
echo.
goto end

:failed
echo.
echo ========================================
echo ALL BUILD METHODS FAILED
echo ========================================
echo.
echo Possible issues:
echo 1. Qt not installed or not in PATH
echo 2. Wrong Qt version (need Qt 6.x)
echo 3. Missing OpenGL support
echo 4. Compiler issues
echo.
echo Try:
echo 1. Install Qt 6.x from https://www.qt.io/download
echo 2. Open Qt Creator and use one of the .pro files
echo 3. Check TROUBLESHOOTING.md
echo.

:end
pause
