#include "NoiseGenerator.h"
#include <cmath>
#include <algorithm>

NoiseGenerator::NoiseGenerator(int seed)
{
    initializePermutation(seed);
}

void NoiseGenerator::initializePermutation(int seed)
{
    // Initialize permutation table
    m_permutation.resize(512);
    
    // Fill with values 0-255
    for (int i = 0; i < 256; ++i) {
        m_permutation[i] = i;
    }
    
    // Shuffle using seed
    std::mt19937 rng(seed);
    std::shuffle(m_permutation.begin(), m_permutation.begin() + 256, rng);
    
    // Duplicate for easier indexing
    for (int i = 0; i < 256; ++i) {
        m_permutation[256 + i] = m_permutation[i];
    }
}

float NoiseGenerator::fade(float t) const
{
    return t * t * t * (t * (t * 6 - 15) + 10);
}

float NoiseGenerator::lerp(float t, float a, float b) const
{
    return a + t * (b - a);
}

float NoiseGenerator::grad(int hash, float x, float y) const
{
    int h = hash & 3;
    float u = h < 2 ? x : y;
    float v = h < 2 ? y : x;
    return ((h & 1) ? -u : u) + ((h & 2) ? -2.0f * v : 2.0f * v);
}

float NoiseGenerator::grad(int hash, float x, float y, float z) const
{
    int h = hash & 15;
    float u = h < 8 ? x : y;
    float v = h < 4 ? y : (h == 12 || h == 14 ? x : z);
    return ((h & 1) ? -u : u) + ((h & 2) ? -v : v);
}

float NoiseGenerator::noise2D(float x, float y) const
{
    // Find unit square that contains point
    int X = static_cast<int>(std::floor(x)) & 255;
    int Y = static_cast<int>(std::floor(y)) & 255;
    
    // Find relative x, y of point in square
    x -= std::floor(x);
    y -= std::floor(y);
    
    // Compute fade curves for x, y
    float u = fade(x);
    float v = fade(y);
    
    // Hash coordinates of square corners
    int A = m_permutation[X] + Y;
    int B = m_permutation[X + 1] + Y;
    
    // Blend the results from the four corners
    return lerp(v,
        lerp(u, grad(m_permutation[A], x, y),
                grad(m_permutation[B], x - 1, y)),
        lerp(u, grad(m_permutation[A + 1], x, y - 1),
                grad(m_permutation[B + 1], x - 1, y - 1)));
}

float NoiseGenerator::noise3D(float x, float y, float z) const
{
    // Find unit cube that contains point
    int X = static_cast<int>(std::floor(x)) & 255;
    int Y = static_cast<int>(std::floor(y)) & 255;
    int Z = static_cast<int>(std::floor(z)) & 255;
    
    // Find relative x, y, z of point in cube
    x -= std::floor(x);
    y -= std::floor(y);
    z -= std::floor(z);
    
    // Compute fade curves for x, y, z
    float u = fade(x);
    float v = fade(y);
    float w = fade(z);
    
    // Hash coordinates of cube corners
    int A = m_permutation[X] + Y;
    int AA = m_permutation[A] + Z;
    int AB = m_permutation[A + 1] + Z;
    int B = m_permutation[X + 1] + Y;
    int BA = m_permutation[B] + Z;
    int BB = m_permutation[B + 1] + Z;
    
    // Blend the results from the eight corners
    return lerp(w,
        lerp(v,
            lerp(u, grad(m_permutation[AA], x, y, z),
                    grad(m_permutation[BA], x - 1, y, z)),
            lerp(u, grad(m_permutation[AB], x, y - 1, z),
                    grad(m_permutation[BB], x - 1, y - 1, z))),
        lerp(v,
            lerp(u, grad(m_permutation[AA + 1], x, y, z - 1),
                    grad(m_permutation[BA + 1], x - 1, y, z - 1)),
            lerp(u, grad(m_permutation[AB + 1], x, y - 1, z - 1),
                    grad(m_permutation[BB + 1], x - 1, y - 1, z - 1))));
}

float NoiseGenerator::octaveNoise2D(float x, float y, int octaves, float persistence, float scale) const
{
    float total = 0;
    float frequency = scale;
    float amplitude = 1;
    float maxValue = 0;
    
    for (int i = 0; i < octaves; ++i) {
        total += noise2D(x * frequency, y * frequency) * amplitude;
        maxValue += amplitude;
        amplitude *= persistence;
        frequency *= 2;
    }
    
    return total / maxValue;
}

float NoiseGenerator::octaveNoise3D(float x, float y, float z, int octaves, float persistence, float scale) const
{
    float total = 0;
    float frequency = scale;
    float amplitude = 1;
    float maxValue = 0;
    
    for (int i = 0; i < octaves; ++i) {
        total += noise3D(x * frequency, y * frequency, z * frequency) * amplitude;
        maxValue += amplitude;
        amplitude *= persistence;
        frequency *= 2;
    }
    
    return total / maxValue;
}

float NoiseGenerator::ridgedNoise2D(float x, float y, int octaves, float scale) const
{
    float total = 0;
    float frequency = scale;
    float amplitude = 1;
    float maxValue = 0;
    
    for (int i = 0; i < octaves; ++i) {
        float n = std::abs(noise2D(x * frequency, y * frequency));
        n = 1.0f - n;
        n = n * n;
        total += n * amplitude;
        maxValue += amplitude;
        amplitude *= 0.5f;
        frequency *= 2;
    }
    
    return total / maxValue;
}

float NoiseGenerator::billowNoise2D(float x, float y, int octaves, float scale) const
{
    float total = 0;
    float frequency = scale;
    float amplitude = 1;
    float maxValue = 0;
    
    for (int i = 0; i < octaves; ++i) {
        float n = std::abs(noise2D(x * frequency, y * frequency));
        total += n * amplitude;
        maxValue += amplitude;
        amplitude *= 0.5f;
        frequency *= 2;
    }
    
    return total / maxValue;
}
