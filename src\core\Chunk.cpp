#include "Chunk.h"
#include "graphics/Mesh.h"
#include <cstring>

Chunk::Chunk(int x, int z)
    : m_x(x)
    , m_z(z)
    , m_blocks(CHUNK_SIZE * CHUNK_HEIGHT * CHUNK_SIZE)
    , m_needsRebuild(true)
    , m_generated(false)
{
    std::memset(m_neighbors, 0, sizeof(m_neighbors));
}

Chunk::~Chunk()
{
}

Block Chunk::getBlock(int x, int y, int z) const
{
    if (!isInBounds(x, y, z)) {
        return Block(BlockType::Air);
    }
    
    return m_blocks[getBlockIndex(x, y, z)];
}

void Chunk::setBlock(int x, int y, int z, const Block& block)
{
    if (!isInBounds(x, y, z)) {
        return;
    }
    
    m_blocks[getBlockIndex(x, y, z)] = block;
    m_needsRebuild = true;
}

bool Chunk::isInBounds(int x, int y, int z) const
{
    return x >= 0 && x < CHUNK_SIZE &&
           y >= 0 && y < CHUNK_HEIGHT &&
           z >= 0 && z < CHUNK_SIZE;
}

int Chunk::getBlockIndex(int x, int y, int z) const
{
    return x + z * CHUNK_SIZE + y * CHUNK_SIZE * CHUNK_SIZE;
}

void Chunk::setNeighbor(int direction, Chunk* neighbor)
{
    if (direction >= 0 && direction < 4) {
        m_neighbors[direction] = neighbor;
    }
}

Chunk* Chunk::getNeighbor(int direction) const
{
    if (direction >= 0 && direction < 4) {
        return m_neighbors[direction];
    }
    return nullptr;
}

Block Chunk::getBlockSafe(int x, int y, int z) const
{
    // Check if coordinates are within this chunk
    if (isInBounds(x, y, z)) {
        return getBlock(x, y, z);
    }
    
    // Check neighbors for blocks outside this chunk
    if (x < 0 && m_neighbors[WEST]) {
        return m_neighbors[WEST]->getBlock(CHUNK_SIZE - 1, y, z);
    }
    if (x >= CHUNK_SIZE && m_neighbors[EAST]) {
        return m_neighbors[EAST]->getBlock(0, y, z);
    }
    if (z < 0 && m_neighbors[NORTH]) {
        return m_neighbors[NORTH]->getBlock(x, y, CHUNK_SIZE - 1);
    }
    if (z >= CHUNK_SIZE && m_neighbors[SOUTH]) {
        return m_neighbors[SOUTH]->getBlock(x, y, 0);
    }
    
    // Default to air for out-of-bounds blocks
    return Block(BlockType::Air);
}

void Chunk::generateMesh()
{
    if (!m_needsRebuild) {
        return;
    }
    
    std::vector<float> vertices;
    std::vector<unsigned int> indices;
    
    // Face vertices (position + texture coordinates)
    const float faceVertices[6][4][5] = {
        // Front face (positive Z)
        {{0, 0, 1, 0, 1}, {1, 0, 1, 1, 1}, {1, 1, 1, 1, 0}, {0, 1, 1, 0, 0}},
        // Back face (negative Z)
        {{1, 0, 0, 0, 1}, {0, 0, 0, 1, 1}, {0, 1, 0, 1, 0}, {1, 1, 0, 0, 0}},
        // Left face (negative X)
        {{0, 0, 0, 0, 1}, {0, 0, 1, 1, 1}, {0, 1, 1, 1, 0}, {0, 1, 0, 0, 0}},
        // Right face (positive X)
        {{1, 0, 1, 0, 1}, {1, 0, 0, 1, 1}, {1, 1, 0, 1, 0}, {1, 1, 1, 0, 0}},
        // Top face (positive Y)
        {{0, 1, 1, 0, 1}, {1, 1, 1, 1, 1}, {1, 1, 0, 1, 0}, {0, 1, 0, 0, 0}},
        // Bottom face (negative Y)
        {{0, 0, 0, 0, 1}, {1, 0, 0, 1, 1}, {1, 0, 1, 1, 0}, {0, 0, 1, 0, 0}}
    };
    
    for (int x = 0; x < CHUNK_SIZE; ++x) {
        for (int y = 0; y < CHUNK_HEIGHT; ++y) {
            for (int z = 0; z < CHUNK_SIZE; ++z) {
                Block block = getBlock(x, y, z);
                if (block.isAir()) {
                    continue;
                }
                
                Vector3 blockPos(x, y, z);
                
                // Check each face
                for (int face = 0; face < 6; ++face) {
                    Vector3 neighborPos = blockPos;
                    
                    // Get neighbor position based on face
                    switch (face) {
                        case Block::FRONT:  neighborPos.z += 1; break;
                        case Block::BACK:   neighborPos.z -= 1; break;
                        case Block::LEFT:   neighborPos.x -= 1; break;
                        case Block::RIGHT:  neighborPos.x += 1; break;
                        case Block::TOP:    neighborPos.y += 1; break;
                        case Block::BOTTOM: neighborPos.y -= 1; break;
                    }
                    
                    Block neighbor = getBlockSafe(neighborPos.x, neighborPos.y, neighborPos.z);
                    
                    if (Block::shouldRenderFace(block.getType(), neighbor.getType())) {
                        Block::TextureCoords texCoords = block.getTextureCoords(face);
                        addFace(vertices, indices, blockPos, face, texCoords);
                    }
                }
            }
        }
    }
    
    // Create or update mesh
    if (!m_mesh) {
        m_mesh = std::make_unique<Mesh>();
    }
    
    m_mesh->updateData(vertices, indices);
    m_needsRebuild = false;
}

void Chunk::addFace(std::vector<float>& vertices, std::vector<unsigned int>& indices,
                    const Vector3& pos, int face, const Block::TextureCoords& texCoords)
{
    unsigned int baseIndex = vertices.size() / 5; // 5 floats per vertex (3 pos + 2 tex)
    
    // Face vertices with texture coordinates
    const float faceVertices[6][4][5] = {
        // Front, Back, Left, Right, Top, Bottom faces...
        // (Same as in generateMesh function)
        {{0, 0, 1, 0, 1}, {1, 0, 1, 1, 1}, {1, 1, 1, 1, 0}, {0, 1, 1, 0, 0}},
        {{1, 0, 0, 0, 1}, {0, 0, 0, 1, 1}, {0, 1, 0, 1, 0}, {1, 1, 0, 0, 0}},
        {{0, 0, 0, 0, 1}, {0, 0, 1, 1, 1}, {0, 1, 1, 1, 0}, {0, 1, 0, 0, 0}},
        {{1, 0, 1, 0, 1}, {1, 0, 0, 1, 1}, {1, 1, 0, 1, 0}, {1, 1, 1, 0, 0}},
        {{0, 1, 1, 0, 1}, {1, 1, 1, 1, 1}, {1, 1, 0, 1, 0}, {0, 1, 0, 0, 0}},
        {{0, 0, 0, 0, 1}, {1, 0, 0, 1, 1}, {1, 0, 1, 1, 0}, {0, 0, 1, 0, 0}}
    };
    
    // Add vertices
    for (int i = 0; i < 4; ++i) {
        // Position
        vertices.push_back(pos.x + faceVertices[face][i][0]);
        vertices.push_back(pos.y + faceVertices[face][i][1]);
        vertices.push_back(pos.z + faceVertices[face][i][2]);
        
        // Texture coordinates
        float u = texCoords.u1 + (texCoords.u2 - texCoords.u1) * faceVertices[face][i][3];
        float v = texCoords.v1 + (texCoords.v2 - texCoords.v1) * faceVertices[face][i][4];
        vertices.push_back(u);
        vertices.push_back(v);
    }
    
    // Add indices (two triangles per face)
    indices.push_back(baseIndex + 0);
    indices.push_back(baseIndex + 1);
    indices.push_back(baseIndex + 2);
    
    indices.push_back(baseIndex + 0);
    indices.push_back(baseIndex + 2);
    indices.push_back(baseIndex + 3);
}
