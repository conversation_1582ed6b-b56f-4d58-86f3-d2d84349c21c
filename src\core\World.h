#pragma once

#include "Chunk.h"
#include "math/Vector3.h"
#include <unordered_map>
#include <memory>

class TerrainGenerator;

class World
{
public:
    explicit World(int seed);
    ~World();
    
    // Chunk management
    Chunk* getChunk(int x, int z);
    Chunk* getChunkAt(const Vector3& worldPos);
    void generateAroundPosition(const Vector3& position, int radius = 8);
    void update(const Vector3& cameraPosition);
    
    // Block access
    Block getBlock(int x, int y, int z);
    void setBlock(int x, int y, int z, const Block& block);
    
    // Rendering
    void render() const;
    std::vector<Chunk*> getVisibleChunks(const Vector3& cameraPos, float renderDistance) const;
    
    // World properties
    int getSeed() const { return m_seed; }
    
    // Utility
    static void worldToChunkCoords(int worldX, int worldZ, int& chunkX, int& chunkZ);
    static void worldToLocalCoords(int worldX, int worldZ, int& localX, int& localZ);
    
private:
    struct ChunkKey
    {
        int x, z;
        
        bool operator==(const ChunkKey& other) const {
            return x == other.x && z == other.z;
        }
    };
    
    struct ChunkKeyHash
    {
        std::size_t operator()(const ChunkKey& key) const {
            return std::hash<int>()(key.x) ^ (std::hash<int>()(key.z) << 1);
        }
    };
    
    void generateChunk(int chunkX, int chunkZ);
    void updateChunkNeighbors(Chunk* chunk);
    void unloadDistantChunks(const Vector3& centerPos, float maxDistance);
    
    int m_seed;
    std::unique_ptr<TerrainGenerator> m_terrainGenerator;
    std::unordered_map<ChunkKey, std::unique_ptr<Chunk>, ChunkKeyHash> m_chunks;
    
    Vector3 m_lastUpdatePosition;
    float m_renderDistance;
    float m_unloadDistance;
};
