# Quick Start Guide - Minecraft Clone

## Opening in Qt Creator

1. **Install Qt 6.x** with Qt Creator
2. **Open Qt Creator**
3. **File → Open File or Project**
4. **Select `MinecraftClone.pro`** from this directory
5. **Configure the project** (select your Qt kit)
6. **Build and Run** (Ctrl+R or click the green play button)

## Project Structure

```
MinecraftClone/
├── src/
│   ├── main.cpp              # Application entry point
│   ├── core/                 # Game logic
│   │   ├── Game.cpp/h        # Main game class
│   │   ├── World.cpp/h       # World management
│   │   ├── Chunk.cpp/h       # Chunk system
│   │   └── Block.cpp/h       # Block definitions
│   ├── graphics/             # Rendering system
│   │   ├── Renderer.cpp/h    # OpenGL renderer
│   │   ├── Camera.cpp/h      # Camera system
│   │   └── Mesh.cpp/h        # Mesh management
│   ├── world/                # Terrain generation
│   │   ├── TerrainGenerator.cpp/h
│   │   └── NoiseGenerator.cpp/h
│   ├── textures/             # SVG texture system
│   │   ├── TextureManager.cpp/h
│   │   └── SvgTextureGenerator.cpp/h
│   ├── math/                 # Math utilities
│   │   ├── Vector3.cpp/h
│   │   └── Matrix4.cpp/h
│   └── ui/                   # User interface
│       ├── MainWindow.cpp/h
│       └── GameWidget.cpp/h
├── resources/                # Game resources
│   └── textures/            # SVG texture files
├── MinecraftClone.pro       # Qt project file
└── resources.qrc            # Qt resource file
```

## Controls

- **WASD**: Move around
- **Mouse**: Look around (click in game area to capture mouse)
- **Space**: Move up
- **Shift**: Move down
- **Mouse Wheel**: Adjust movement speed
- **Escape**: Release mouse cursor

## Building from Command Line

If you prefer command line:

```bash
qmake MinecraftClone.pro
make
```

Or on Windows:
```cmd
qmake MinecraftClone.pro
nmake
```

## Troubleshooting

### Common Issues:

1. **Qt not found**: Make sure Qt 6.x is installed and in your PATH
2. **OpenGL errors**: Ensure your graphics drivers support OpenGL 3.3+
3. **Build errors**: Check that all Qt modules are installed (core, widgets, opengl, openglwidgets, svg)

### Debug Mode:

To run in debug mode for better error messages:
- In Qt Creator: Select "Debug" build configuration
- Command line: `qmake CONFIG+=debug`

## Features Implemented

✅ **3D Voxel World**: Infinite procedurally generated world  
✅ **Random Terrain**: Perlin noise-based generation  
✅ **SVG Textures**: Procedural texture generation  
✅ **First-Person Camera**: WASD + mouse controls  
✅ **Multiple Biomes**: Plains, Forest, Desert, Mountains, Ocean  
✅ **Chunk System**: Efficient world loading/unloading  
✅ **OpenGL Rendering**: Hardware-accelerated graphics  

## Next Steps

The basic framework is complete! You can now:

1. **Add block interaction** (placement/destruction)
2. **Implement inventory system**
3. **Add lighting and shadows**
4. **Create more block types**
5. **Add multiplayer support**
6. **Implement save/load functionality**

Enjoy building your Minecraft clone! 🎮
