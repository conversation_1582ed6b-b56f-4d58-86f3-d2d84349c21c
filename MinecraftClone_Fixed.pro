QT += core widgets opengl openglwidgets svg

CONFIG += c++17

TARGET = MinecraftClone
TEMPLATE = app

# Force MOC to work properly
CONFIG += no_batch

# Source files (all in one list to avoid path issues)
SOURCES += \
    src/main.cpp \
    src/core/Game.cpp \
    src/core/Block.cpp \
    src/core/Chunk.cpp \
    src/core/World.cpp \
    src/graphics/Renderer.cpp \
    src/graphics/Camera.cpp \
    src/graphics/Mesh.cpp \
    src/world/TerrainGenerator.cpp \
    src/world/NoiseGenerator.cpp \
    src/ui/MainWindow.cpp \
    src/ui/GameWidget.cpp \
    src/textures/TextureManager.cpp \
    src/textures/SvgTextureGenerator.cpp \
    src/math/Vector3.cpp \
    src/math/Matrix4.cpp

# Header files (all in one list)
HEADERS += \
    src/core/Game.h \
    src/core/Block.h \
    src/core/Chunk.h \
    src/core/World.h \
    src/graphics/Renderer.h \
    src/graphics/Camera.h \
    src/graphics/Mesh.h \
    src/world/TerrainGenerator.h \
    src/world/NoiseGenerator.h \
    src/ui/MainWindow.h \
    src/ui/GameWidget.h \
    src/textures/TextureManager.h \
    src/textures/SvgTextureGenerator.h \
    src/math/Vector3.h \
    src/math/Matrix4.h

# Include paths
INCLUDEPATH += \
    src \
    src/core \
    src/graphics \
    src/world \
    src/ui \
    src/textures \
    src/math

# Resources
RESOURCES += resources.qrc

# Platform-specific libraries
win32 {
    LIBS += -lopengl32
}

unix {
    LIBS += -lGL
}

# Ensure MOC files are generated in the right place
MOC_DIR = .
OBJECTS_DIR = .
RCC_DIR = .
UI_DIR = .
