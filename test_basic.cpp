#include <iostream>
#include "src/math/Vector3.h"
#include "src/math/Matrix4.h"
#include "src/core/Block.h"

int main()
{
    std::cout << "Testing Minecraft Clone Core Classes..." << std::endl;
    
    // Test Vector3
    Vector3 v1(1, 2, 3);
    Vector3 v2(4, 5, 6);
    Vector3 v3 = v1 + v2;
    
    std::cout << "Vector3 test: (" << v3.x << ", " << v3.y << ", " << v3.z << ")" << std::endl;
    std::cout << "Vector3 length: " << v3.length() << std::endl;
    
    // Test Matrix4
    Matrix4 identity = Matrix4::identity();
    Matrix4 translation = Matrix4::translation(Vector3(10, 20, 30));
    
    std::cout << "Matrix4 identity[0]: " << identity[0] << std::endl;
    std::cout << "Matrix4 translation[12]: " << translation[12] << std::endl;
    
    // Test Block
    Block grassBlock(BlockType::Grass);
    std::cout << "Block type: " << Block::getBlockName(grassBlock.getType()) << std::endl;
    std::cout << "Block is solid: " << (grassBlock.isSolid() ? "true" : "false") << std::endl;
    
    std::cout << "All basic tests passed!" << std::endl;
    return 0;
}
