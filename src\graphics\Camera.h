#pragma once

#include "math/Vector3.h"
#include "math/Matrix4.h"

class Camera
{
public:
    Camera();
    ~Camera();
    
    // Position and rotation
    void setPosition(const Vector3& position) { m_position = position; updateViewMatrix(); }
    void setRotation(const Vector3& rotation) { m_rotation = rotation; updateViewMatrix(); }
    
    Vector3 getPosition() const { return m_position; }
    Vector3 getRotation() const { return m_rotation; }
    
    // Movement
    void move(const Vector3& delta) { m_position += delta; updateViewMatrix(); }
    void rotate(const Vector3& delta) { m_rotation += delta; updateViewMatrix(); }
    
    // Direction vectors
    Vector3 getForward() const;
    Vector3 getRight() const;
    Vector3 getUp() const;
    
    // Matrices
    Matrix4 getViewMatrix() const { return m_viewMatrix; }
    Matrix4 getProjectionMatrix() const { return m_projectionMatrix; }
    Matrix4 getViewProjectionMatrix() const { return m_projectionMatrix * m_viewMatrix; }
    
    // Projection settings
    void setPerspective(float fov, float aspectRatio, float nearPlane, float farPlane);
    void setAspectRatio(float aspectRatio);
    
    float getFov() const { return m_fov; }
    float getAspectRatio() const { return m_aspectRatio; }
    float getNearPlane() const { return m_nearPlane; }
    float getFarPlane() const { return m_farPlane; }
    
    // Update
    void update(float deltaTime);
    
private:
    void updateViewMatrix();
    void updateProjectionMatrix();
    
    Vector3 m_position;
    Vector3 m_rotation; // Euler angles in degrees (pitch, yaw, roll)
    
    Matrix4 m_viewMatrix;
    Matrix4 m_projectionMatrix;
    
    // Projection parameters
    float m_fov;
    float m_aspectRatio;
    float m_nearPlane;
    float m_farPlane;
    
    // Cached direction vectors
    mutable Vector3 m_forward;
    mutable Vector3 m_right;
    mutable Vector3 m_up;
    mutable bool m_directionsDirty;
};
