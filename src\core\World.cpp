#include "World.h"
#include "world/TerrainGenerator.h"
#include <cmath>

World::World(int seed)
    : m_seed(seed)
    , m_terrainGenerator(std::make_unique<TerrainGenerator>(seed))
    , m_lastUpdatePosition(0, 0, 0)
    , m_renderDistance(128.0f)
    , m_unloadDistance(192.0f)
{
}

World::~World()
{
}

Chunk* World::getChunk(int x, int z)
{
    ChunkKey key{x, z};
    auto it = m_chunks.find(key);
    if (it != m_chunks.end()) {
        return it->second.get();
    }
    return nullptr;
}

Chunk* World::getChunkAt(const Vector3& worldPos)
{
    int chunkX, chunkZ;
    worldToChunkCoords(worldPos.x, worldPos.z, chunkX, chunkZ);
    return getChunk(chunkX, chunkZ);
}

void World::generateAroundPosition(const Vector3& position, int radius)
{
    int centerChunkX, centerChunkZ;
    worldToChunkCoords(position.x, position.z, centerChunkX, centerChunkZ);
    
    for (int x = centerChunkX - radius; x <= centerChunkX + radius; ++x) {
        for (int z = centerChunkZ - radius; z <= centerChunkZ + radius; ++z) {
            if (!getChunk(x, z)) {
                generateChunk(x, z);
            }
        }
    }
}

void World::update(const Vector3& cameraPosition)
{
    // Check if we need to generate new chunks
    float distanceFromLastUpdate = (cameraPosition - m_lastUpdatePosition).length();
    if (distanceFromLastUpdate > 32.0f) { // Update every 32 blocks
        generateAroundPosition(cameraPosition, 8);
        unloadDistantChunks(cameraPosition, m_unloadDistance);
        m_lastUpdatePosition = cameraPosition;
    }
    
    // Update chunk meshes that need rebuilding
    for (auto& pair : m_chunks) {
        Chunk* chunk = pair.second.get();
        if (chunk->needsRebuild()) {
            chunk->generateMesh();
        }
    }
}

Block World::getBlock(int x, int y, int z)
{
    int chunkX, chunkZ;
    worldToChunkCoords(x, z, chunkX, chunkZ);
    
    Chunk* chunk = getChunk(chunkX, chunkZ);
    if (!chunk) {
        return Block(BlockType::Air);
    }
    
    int localX, localZ;
    worldToLocalCoords(x, z, localX, localZ);
    
    return chunk->getBlock(localX, y, localZ);
}

void World::setBlock(int x, int y, int z, const Block& block)
{
    int chunkX, chunkZ;
    worldToChunkCoords(x, z, chunkX, chunkZ);
    
    Chunk* chunk = getChunk(chunkX, chunkZ);
    if (!chunk) {
        return;
    }
    
    int localX, localZ;
    worldToLocalCoords(x, z, localX, localZ);
    
    chunk->setBlock(localX, y, localZ, block);
    
    // Mark neighboring chunks for rebuild if block is on chunk boundary
    if (localX == 0) {
        Chunk* neighbor = getChunk(chunkX - 1, chunkZ);
        if (neighbor) neighbor->rebuildMesh();
    }
    if (localX == Chunk::CHUNK_SIZE - 1) {
        Chunk* neighbor = getChunk(chunkX + 1, chunkZ);
        if (neighbor) neighbor->rebuildMesh();
    }
    if (localZ == 0) {
        Chunk* neighbor = getChunk(chunkX, chunkZ - 1);
        if (neighbor) neighbor->rebuildMesh();
    }
    if (localZ == Chunk::CHUNK_SIZE - 1) {
        Chunk* neighbor = getChunk(chunkX, chunkZ + 1);
        if (neighbor) neighbor->rebuildMesh();
    }
}

std::vector<Chunk*> World::getVisibleChunks(const Vector3& cameraPos, float renderDistance) const
{
    std::vector<Chunk*> visibleChunks;
    
    for (const auto& pair : m_chunks) {
        Chunk* chunk = pair.second.get();
        Vector3 chunkCenter = chunk->getWorldPosition() + Vector3(Chunk::CHUNK_SIZE / 2, 0, Chunk::CHUNK_SIZE / 2);
        
        float distance = (chunkCenter - cameraPos).length();
        if (distance <= renderDistance) {
            visibleChunks.push_back(chunk);
        }
    }
    
    return visibleChunks;
}

void World::worldToChunkCoords(int worldX, int worldZ, int& chunkX, int& chunkZ)
{
    chunkX = worldX >= 0 ? worldX / Chunk::CHUNK_SIZE : (worldX - Chunk::CHUNK_SIZE + 1) / Chunk::CHUNK_SIZE;
    chunkZ = worldZ >= 0 ? worldZ / Chunk::CHUNK_SIZE : (worldZ - Chunk::CHUNK_SIZE + 1) / Chunk::CHUNK_SIZE;
}

void World::worldToLocalCoords(int worldX, int worldZ, int& localX, int& localZ)
{
    localX = worldX >= 0 ? worldX % Chunk::CHUNK_SIZE : (Chunk::CHUNK_SIZE - 1) + ((worldX + 1) % Chunk::CHUNK_SIZE);
    localZ = worldZ >= 0 ? worldZ % Chunk::CHUNK_SIZE : (Chunk::CHUNK_SIZE - 1) + ((worldZ + 1) % Chunk::CHUNK_SIZE);
}

void World::generateChunk(int chunkX, int chunkZ)
{
    ChunkKey key{chunkX, chunkZ};
    if (m_chunks.find(key) != m_chunks.end()) {
        return; // Chunk already exists
    }
    
    auto chunk = std::make_unique<Chunk>(chunkX, chunkZ);
    
    // Generate terrain
    m_terrainGenerator->generateChunk(*chunk);
    
    // Update neighbors
    updateChunkNeighbors(chunk.get());
    
    // Store chunk
    m_chunks[key] = std::move(chunk);
}

void World::updateChunkNeighbors(Chunk* chunk)
{
    int x = chunk->getX();
    int z = chunk->getZ();
    
    // Set neighbors for this chunk
    chunk->setNeighbor(Chunk::NORTH, getChunk(x, z - 1));
    chunk->setNeighbor(Chunk::SOUTH, getChunk(x, z + 1));
    chunk->setNeighbor(Chunk::EAST, getChunk(x + 1, z));
    chunk->setNeighbor(Chunk::WEST, getChunk(x - 1, z));
    
    // Update neighboring chunks to point to this chunk
    Chunk* north = getChunk(x, z - 1);
    if (north) north->setNeighbor(Chunk::SOUTH, chunk);
    
    Chunk* south = getChunk(x, z + 1);
    if (south) south->setNeighbor(Chunk::NORTH, chunk);
    
    Chunk* east = getChunk(x + 1, z);
    if (east) east->setNeighbor(Chunk::WEST, chunk);
    
    Chunk* west = getChunk(x - 1, z);
    if (west) west->setNeighbor(Chunk::EAST, chunk);
}

void World::unloadDistantChunks(const Vector3& centerPos, float maxDistance)
{
    auto it = m_chunks.begin();
    while (it != m_chunks.end()) {
        Chunk* chunk = it->second.get();
        Vector3 chunkCenter = chunk->getWorldPosition() + Vector3(Chunk::CHUNK_SIZE / 2, 0, Chunk::CHUNK_SIZE / 2);
        
        float distance = (chunkCenter - centerPos).length();
        if (distance > maxDistance) {
            it = m_chunks.erase(it);
        } else {
            ++it;
        }
    }
}
