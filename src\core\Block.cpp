#include "Block.h"

Block::TextureCoords Block::getTextureCoords(int face) const
{
    // Texture atlas is 4x4 grid (16 textures)
    const float texSize = 1.0f / 4.0f;
    
    int texIndex = 0;
    
    switch (m_type) {
        case BlockType::Grass:
            if (face == TOP) texIndex = 0;      // Grass top
            else if (face == BOTTOM) texIndex = 2; // Dirt
            else texIndex = 1;                   // Grass side
            break;
        case BlockType::Dirt:
            texIndex = 2;
            break;
        case BlockType::Stone:
            texIndex = 3;
            break;
        case BlockType::Wood:
            if (face == TOP || face == BOTTOM) texIndex = 5; // Wood top/bottom
            else texIndex = 4;                               // Wood side
            break;
        case BlockType::Leaves:
            texIndex = 6;
            break;
        case BlockType::Sand:
            texIndex = 7;
            break;
        case BlockType::Water:
            texIndex = 8;
            break;
        case BlockType::Bedrock:
            texIndex = 9;
            break;
        default:
            texIndex = 0;
            break;
    }
    
    // Calculate UV coordinates
    int x = texIndex % 4;
    int y = texIndex / 4;
    
    TextureCoords coords;
    coords.u1 = x * texSize;
    coords.v1 = y * texSize;
    coords.u2 = (x + 1) * texSize;
    coords.v2 = (y + 1) * texSize;
    
    return coords;
}

const char* Block::getBlockName(BlockType type)
{
    switch (type) {
        case BlockType::Air: return "Air";
        case BlockType::Grass: return "Grass";
        case BlockType::Dirt: return "Dirt";
        case BlockType::Stone: return "Stone";
        case BlockType::Wood: return "Wood";
        case BlockType::Leaves: return "Leaves";
        case BlockType::Sand: return "Sand";
        case BlockType::Water: return "Water";
        case BlockType::Bedrock: return "Bedrock";
        default: return "Unknown";
    }
}

bool Block::shouldRenderFace(BlockType current, BlockType neighbor)
{
    // Don't render air
    if (current == BlockType::Air) {
        return false;
    }
    
    // Always render if neighbor is air
    if (neighbor == BlockType::Air) {
        return true;
    }
    
    // Don't render faces between identical solid blocks
    if (current == neighbor && current != BlockType::Water && current != BlockType::Leaves) {
        return false;
    }
    
    // Render transparent blocks against solid blocks
    if ((current == BlockType::Water || current == BlockType::Leaves) && 
        (neighbor != BlockType::Water && neighbor != BlockType::Leaves)) {
        return true;
    }
    
    // Render solid blocks against transparent blocks
    if ((current != BlockType::Water && current != BlockType::Leaves) &&
        (neighbor == BlockType::Water || neighbor == BlockType::Leaves)) {
        return true;
    }
    
    return false;
}
