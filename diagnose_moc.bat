@echo off
echo ========================================
echo MOC Issue Diagnostic
echo ========================================

echo.
echo Checking for Q_OBJECT macros in source files...
echo.

findstr /s /i "Q_OBJECT" src\*.h
if errorlevel 1 (
    echo No Q_OBJECT macros found in headers.
) else (
    echo.
    echo ^^^ These files contain Q_OBJECT macros and need MOC compilation.
)

echo.
echo Checking for existing MOC files...
dir /b moc_*.cpp 2>nul
if errorlevel 1 (
    echo No MOC files found.
) else (
    echo ^^^ These MOC files exist.
)

echo.
echo Checking for .moc files...
dir /b *.moc 2>nul
if errorlevel 1 (
    echo No .moc files found.
) else (
    echo ^^^ These .moc files exist.
)

echo.
echo Checking Makefile for MOC references...
if exist Makefile (
    findstr /i "moc" Makefile
    if errorlevel 1 (
        echo No MOC references in Makefile.
    ) else (
        echo ^^^ These are MOC references in Makefile.
    )
) else (
    echo No Makefile found.
)

echo.
echo ========================================
echo Recommendation:
echo ========================================
echo.
echo Use MinecraftClone_Clean.pro which excludes:
echo - src/ui/MainWindow.h/cpp (has Q_OBJECT)
echo - src/ui/GameWidget.h/cpp (has Q_OBJECT)
echo.
echo And uses instead:
echo - src/main_nomoc.cpp (no Q_OBJECT)
echo - src/ui/BasicGameWidget.h/cpp (no Q_OBJECT)
echo.
echo Run: build_clean.bat
echo.
pause
