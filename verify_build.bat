@echo off
echo ========================================
echo Minecraft Clone - Build Verification
echo ========================================
echo.

echo Checking project files...
if not exist "MinecraftClone.pro" (
    echo ERROR: MinecraftClone.pro not found!
    pause
    exit /b 1
)

if not exist "src\main.cpp" (
    echo ERROR: src\main.cpp not found!
    pause
    exit /b 1
)

echo ✓ Project files found

echo.
echo Checking Qt installation...
qmake -version >nul 2>&1
if errorlevel 1 (
    echo ERROR: qmake not found! Please install Qt and add it to PATH.
    echo.
    echo Download Qt from: https://www.qt.io/download
    pause
    exit /b 1
)

echo ✓ Qt found
qmake -version

echo.
echo Building core test...
qmake test_core.pro
if errorlevel 1 (
    echo ERROR: Failed to configure core test
    pause
    exit /b 1
)

nmake >nul 2>&1
if errorlevel 1 (
    make >nul 2>&1
    if errorlevel 1 (
        echo ERROR: Failed to build core test
        pause
        exit /b 1
    )
)

echo ✓ Core test built successfully

echo.
echo Running core test...
if exist "test_core.exe" (
    test_core.exe
) else if exist "test_core" (
    ./test_core
) else (
    echo WARNING: Core test executable not found, but build succeeded
)

echo.
echo Cleaning up test files...
del /q *.o >nul 2>&1
del /q Makefile >nul 2>&1
del /q test_core.exe >nul 2>&1
del /q test_core >nul 2>&1

echo.
echo ========================================
echo Build verification complete!
echo ========================================
echo.
echo Next steps:
echo 1. Open Qt Creator
echo 2. File → Open File or Project
echo 3. Select MinecraftClone.pro
echo 4. Build and Run the project
echo.
echo Enjoy your Minecraft clone! 🎮
pause
