@echo off
echo ========================================
echo Building Minecraft Clone with Qt
echo ========================================

REM Clean previous build
call clean_build.bat

echo.
echo Configuring project...
qmake MinecraftClone.pro
if errorlevel 1 (
    echo ERROR: qmake failed!
    pause
    exit /b 1
)

echo.
echo Building project...
REM Try nmake first (Visual Studio), then make (MinGW)
nmake >nul 2>&1
if errorlevel 1 (
    echo nmake failed, trying make...
    make
    if errorlevel 1 (
        echo ERROR: Build failed!
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo Build successful!
echo ========================================
echo.
echo Executable should be in: bin\MinecraftClone.exe
echo.
echo To run in Qt Creator:
echo 1. Open MinecraftClone.pro
echo 2. Configure project
echo 3. Build and Run
echo.
pause
